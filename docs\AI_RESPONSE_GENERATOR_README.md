# AI Response Generator for Google Form AutoFill

This module enhances the Google Form AutoFill tool with advanced AI response generation capabilities, including support for customer-provided examples, style matching, quality control, and feedback mechanisms.

## Features

- **Example-Based Generation**: Generate responses that match the style and content of customer-provided examples
- **Quality Control**: Filter responses based on quality scores to ensure high-quality outputs
- **Diversity Control**: Ensure generated responses are diverse and not repetitive
- **Batch Processing**: Generate large numbers of responses efficiently in batches
- **Feedback System**: Collect and apply customer and expert feedback to improve response quality
- **Style Guidance**: Apply specific style guidelines to different question types
- **Learning Insights**: Extract insights from feedback to improve future generations

## Installation

1. Ensure you have installed the base Google Form AutoFill tool requirements
2. Install additional dependencies:
   ```
   pip install google-generativeai python-dotenv
   ```

3. Set up your Gemini API key:
   - Get an API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Set it as an environment variable:
     ```
     export GEMINI_API_KEY=your_api_key_here  # Linux/Mac
     set GEMINI_API_KEY=your_api_key_here     # Windows
     ```
   - Or create a `.env` file in the project root with your API key:
     ```
     GEMINI_API_KEY=your_api_key_here
     ```
   - For high-volume processing, you can use multiple API keys:
     ```
     GEMINI_API_KEY_1=your_first_api_key
     GEMINI_API_KEY_2=your_second_api_key
     GEMINI_API_KEY_3=your_third_api_key
     ```

## Usage

### Generate Responses with Examples

Generate responses that match the style and content of customer-provided examples:

```bash
python ai_response_generator.py generate-with-examples https://docs.google.com/forms/d/e/your-form-id/viewform examples/example_responses.json
```

Options:
- `--sample-count`: Number of samples to generate per question (default: 5)
- `--quality-threshold`: Minimum quality score for responses (0.0-1.0, default: 0.7)
- `--diversity-factor`: Factor controlling response diversity (0.0-1.0, default: 0.8)
- `--style-guidance`: Path to JSON file with style guidance
- `--api-key`: Gemini API key or comma-separated list of keys
- `--key-strategy`: Strategy for API key selection when multiple keys are provided (round_robin, random, or least_used)

### Batch Generate Responses

Generate a large number of responses efficiently in batches:

```bash
python ai_response_generator.py batch-generate https://docs.google.com/forms/d/e/your-form-id/viewform examples/example_responses.json --batch-size 20 --total-count 200
```

Options:
- `--batch-size`: Number of responses to generate in each batch (default: 10)
- `--total-count`: Total number of responses to generate (default: 100)
- `--quality-threshold`: Minimum quality score for responses (0.0-1.0, default: 0.7)
- `--diversity-factor`: Factor controlling response diversity (0.0-1.0, default: 0.8)
- `--style-guidance`: Path to JSON file with style guidance
- `--api-key`: Gemini API key or comma-separated list of keys
- `--key-strategy`: Strategy for API key selection when multiple keys are provided

### Feedback Management

Add customer feedback:

```bash
python ai_response_generator.py add-customer-feedback your-form-id examples/customer_feedback.json
```

Add expert feedback:

```bash
python ai_response_generator.py add-expert-feedback your-form-id examples/expert_feedback.json
```

Apply feedback to adjust response weights:

```bash
python ai_response_generator.py apply-feedback your-form-id
```

Get feedback summary:

```bash
python ai_response_generator.py feedback-summary your-form-id --output feedback_summary.json
```

Get learning insights:

```bash
python ai_response_generator.py learning-insights your-form-id --output learning_insights.json
```

## File Formats

### Example Responses JSON

```json
{
  "question_id_1": [
    "Example response 1 for question 1",
    "Example response 2 for question 1",
    "Example response 3 for question 1"
  ],
  "question_id_2": [
    "Example response 1 for question 2",
    "Example response 2 for question 2"
  ]
}
```

### Style Guidance JSON

```json
{
  "text": "Responses should be professional but conversational, using first-person perspective.",
  "paragraph": "Responses should be thoughtful and detailed, using professional language with some personal touches.",
  "multiple_choice": "Keep responses balanced between options, with a slight preference for positive choices.",
  "dropdown": "Distribute responses evenly across all reasonable options.",
  "checkboxes": "Select 2-3 options per response on average, focusing on complementary choices."
}
```

### Customer Feedback JSON

```json
{
  "customer_id": "customer123",
  "ratings": {
    "question_id_1": {
      "0": 4.5,
      "1": 3.0,
      "2": 5.0
    },
    "question_id_2": {
      "0": 4.0,
      "1": 4.5
    }
  },
  "comments": {
    "question_id_1": "The responses were excellent, but some could be more specific.",
    "question_id_2": "Good variety, but would like to see more technical details."
  }
}
```

### Expert Feedback JSON

```json
{
  "expert_id": "expert456",
  "response_feedback": {
    "question_id_1": [
      {
        "response_index": 0,
        "type": "approve",
        "comment": "Excellent response that balances multiple factors."
      },
      {
        "response_index": 3,
        "type": "edit",
        "edited_text": "Edited response text goes here.",
        "comment": "Edited to be more specific and action-oriented."
      },
      {
        "response_index": 5,
        "type": "reject",
        "comment": "Too vague and generic. Lacks specific insights."
      }
    ]
  },
  "general_comments": "Overall feedback about the responses goes here."
}
```

## Configuration

You can set default configuration values in a `.env` file:

```
# Gemini API key(s)
GEMINI_API_KEY=your_api_key_here
# Or multiple keys
GEMINI_API_KEY_1=your_first_api_key
GEMINI_API_KEY_2=your_second_api_key

# Key selection strategy (round_robin, random, or least_used)
GEMINI_KEY_STRATEGY=round_robin

# Default form URL
DEFAULT_FORM_URL=https://docs.google.com/forms/d/e/your-form-id/viewform

# Response generation settings
QUALITY_THRESHOLD=0.7
DIVERSITY_FACTOR=0.8
SAMPLE_COUNT=5

# Batch generation settings
BATCH_SIZE=10
TOTAL_COUNT=100
```

## Integration with Other Modules

### API Integration (Developer 2)

The AI Response Generator module is designed to work seamlessly with the API integration module. The generated responses can be accessed through the API endpoints for submission to Google Forms.

### Submission Management (Developer 4)

The generated responses are stored in a format compatible with the submission management module, allowing for easy submission to Google Forms.

## Best Practices

1. **Provide Quality Examples**: The quality of generated responses depends heavily on the quality of provided examples. Provide 3-5 diverse, high-quality examples for each question.

2. **Use Style Guidance**: For consistent results, provide style guidance for different question types.

3. **Adjust Quality and Diversity**: Fine-tune the quality threshold and diversity factor based on your needs:
   - Higher quality threshold (e.g., 0.8) for more conservative, example-matching responses
   - Higher diversity factor (e.g., 0.9) for more varied responses

4. **Batch Processing**: For large forms or high volumes, use batch processing to avoid API rate limits and improve efficiency.

5. **Feedback Loop**: Regularly collect and apply feedback to improve response quality over time.

## Troubleshooting

### API Key Issues

If you encounter API key errors:
- Verify your API key is correct and has not expired
- Check if you've reached your API quota limit
- Try using multiple API keys with the round_robin strategy

### Quality Issues

If response quality is not meeting expectations:
- Provide more and better examples
- Increase the quality threshold
- Add more detailed style guidance
- Collect and apply expert feedback

### Performance Issues

If generation is slow:
- Use batch processing with appropriate batch size
- Use multiple API keys
- Reduce the number of samples per question

## Advanced Usage

### Custom Gemini Model

You can specify a different Gemini model by setting the `GEMINI_MODEL` environment variable:

```
GEMINI_MODEL=gemini-2.0-pro
```

Available models:
- `gemini-2.0-flash-lite` (default, fastest)
- `gemini-2.0-flash`
- `gemini-2.0-pro`

### Response Diversity Control

Fine-tune response diversity by adjusting both the diversity factor and the Gemini temperature:

```bash
python ai_response_generator.py generate-with-examples form_url examples.json --diversity-factor 0.9
```

Set the `GEMINI_TEMPERATURE` environment variable to control randomness (0.0-1.0, default: 0.7):

```
GEMINI_TEMPERATURE=0.8
```

Higher temperature values produce more diverse but potentially less focused responses.
