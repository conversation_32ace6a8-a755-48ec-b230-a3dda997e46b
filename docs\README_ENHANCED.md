# Enhanced Google Form AutoFill and Submit

This enhanced version of the Google Form AutoFill tool provides advanced capabilities for loading forms, generating intelligent responses using Google's Gemini AI, reviewing and editing responses before submission, and tracking submission progress.

## Features

- **Form Loading & Question Display**: Load Google Forms and display all questions with detailed information
- **Multiple Response Generation Methods**:
  - Manual input of samples and weightage
  - Gemini-assisted generation (for open-ended questions only)
  - Fully automated Gemini generation (for open-ended questions only)
- **Manual Weight Adjustment**: Non-open-ended questions (multiple choice, dropdown, checkboxes) are initialized with equal weights for manual adjustment
- **Pre-submission Review & Editing**: Review and CRUD (Create, Read, Update, Delete) all responses before submission
- **Efficient Data Management**: All data is prepared upfront and stored for user review
- **Progress Tracking**: Monitor submission progress with a visual progress bar

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/googleform-autofill-and-submit.git
   cd googleform-autofill-and-submit
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up Gemini API key:
   - Get an API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Set it as an environment variable:
     ```
     export GEMINI_API_KEY=your_api_key_here  # Linux/Mac
     set GEMINI_API_KEY=your_api_key_here     # Windows
     ```
   - Or provide it with the `--api-key` parameter when running commands
   - Or create a `.env` file in the project root (see Configuration section below)

## Usage

The enhanced tool provides a command-line interface with several subcommands:

### Load and Display Form

```
python enhanced_main.py load https://docs.google.com/forms/d/e/your-form-id/viewform
```

This will load the form and display its structure, including all questions, their types, and options.

### Generate Responses

```
python enhanced_main.py generate https://docs.google.com/forms/d/e/your-form-id/viewform --method gemini_assisted --samples 5 --api-key your_api_key_here
```

Available methods:
- `manual`: Initialize the form for manual input of responses
- `gemini_assisted`: Generate responses using Gemini AI for open-ended questions only. Non-open-ended questions (multiple choice, dropdown, checkboxes) are initialized with all options having equal weights.
- `fully_automated`: Generate and weight responses automatically using Gemini AI for open-ended questions only

**Note**: Non-open-ended questions (multiple choice, dropdown, checkboxes) will not use Gemini AI. Instead, all options are initialized with equal weights (weight=1). You can manually adjust these weights using the edit commands.

You can also provide example responses in a JSON file:
```
python enhanced_main.py generate https://docs.google.com/forms/d/e/your-form-id/viewform --method gemini_assisted --examples examples.json
```

Example JSON format:
```json
{
  "question_id_1": [
    "Example response 1",
    "Example response 2"
  ],
  "question_id_2": [
    "Example response 1",
    "Example response 2"
  ]
}
```

### Review Responses

```
python enhanced_main.py review form_id
```

This will display all questions and their prepared responses with weights.

### Edit Responses

Add a new response:
```
python enhanced_main.py add form_id question_index "Response text" --weight 5
```

Edit an existing response:
```
python enhanced_main.py edit form_id question_index response_index "New response text" --weight 10
```

Delete a response:
```
python enhanced_main.py delete form_id question_index response_index
```

Note: `question_index` and `response_index` are 1-based indices.

### Submit Form

```
python enhanced_main.py submit form_id https://docs.google.com/forms/d/e/your-form-id/viewform 10 --delay-min 1.5 --delay-max 4.0
```

This will submit the form 10 times with random responses selected based on their weights, with a delay of 1.5-4.0 seconds between submissions.

## Examples

### Basic Workflow

1. Load and examine a form:
   ```
   python enhanced_main.py load https://docs.google.com/forms/d/e/your-form-id/viewform
   ```

2. Generate responses using Gemini:
   ```
   python enhanced_main.py generate https://docs.google.com/forms/d/e/your-form-id/viewform --method gemini_assisted
   ```
   This will:
   - Use Gemini AI to generate varied responses for open-ended questions
   - Initialize all options with equal weights for multiple choice/dropdown/checkbox questions

3. Review the generated responses:
   ```
   python enhanced_main.py review your-form-id
   ```

4. Adjust weights for multiple choice questions:
   ```
   # For example, if question 2 is a multiple choice with 3 options:
   python enhanced_main.py edit your-form-id 2 1 "Option 1" --weight 50
   python enhanced_main.py edit your-form-id 2 2 "Option 2" --weight 30
   python enhanced_main.py edit your-form-id 2 3 "Option 3" --weight 20
   ```

5. Submit the form multiple times:
   ```
   python enhanced_main.py submit your-form-id https://docs.google.com/forms/d/e/your-form-id/viewform 5
   ```
   The system will randomly select responses based on their weights for each submission.

## Configuration

### Using .env File

You can create a `.env` file in the project root directory to store configuration values. The tool will automatically load these values when it runs. Here's an example `.env` file:

```
# Google Form AutoFill Configuration

# Gemini API key
GEMINI_API_KEY=your_api_key_here

# Default form URL (optional)
DEFAULT_FORM_URL=https://docs.google.com/forms/d/e/your-form-id/viewform

# Submission settings (optional)
DEFAULT_SUBMISSION_COUNT=5
MIN_SUBMISSION_DELAY=1.5
MAX_SUBMISSION_DELAY=3.0

# Response generation settings (optional)
DEFAULT_SAMPLE_COUNT=5
DEFAULT_GENERATION_METHOD=gemini_assisted  # Options: manual, gemini_assisted, fully_automated
```

With this configuration, you can run commands without specifying these values as command-line arguments:

```
# Load the default form
python enhanced_main.py load

# Generate responses using default settings
python enhanced_main.py generate

# Submit the form with default settings
python enhanced_main.py submit your-form-id
```

### Using Environment Variables

You can also set the Gemini API key as an environment variable to avoid passing it in each command:

```
export GEMINI_API_KEY=your_api_key_here  # Linux/Mac
set GEMINI_API_KEY=your_api_key_here     # Windows
```

### Custom Example Responses

Create a JSON file with example responses for specific questions:

```json
{
  "1234567890": [
    "I strongly agree because of the environmental benefits.",
    "I somewhat agree, though there are economic concerns."
  ],
  "0987654321": [
    "The most important factor is cost efficiency.",
    "Quality should always be the primary consideration."
  ]
}
```

Then use it when generating responses:

```
python enhanced_main.py generate https://docs.google.com/forms/d/e/your-form-id/viewform --method gemini_assisted --examples my_examples.json
```

## Limitations

- The tool does not support file upload fields in Google Forms
- Some complex form structures might not be fully supported
- Google may implement rate limiting or anti-bot measures that could affect submission success rates

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Original Google Form AutoFill project
- Google's Gemini AI for intelligent response generation

## Docker Development Environment

This project includes Docker support for easier development and deployment. The Docker configuration files are located in the `docker_files` directory.

### Using Docker for Development

1. Start the development environment:
   ```bash
   # Windows
   docker_files\start-dev.bat
   
   # Linux/Mac
   chmod +x docker_files/start-dev.sh
   ./docker_files/start-dev.sh
   ```

2. This will give you a bash shell inside the Docker container where you can run commands.

### Running the Web Application with Docker

1. Start the web server:
   ```bash
   # Windows
   docker_files\start-web.bat
   
   # Linux/Mac
   chmod +x docker_files/start-web.sh
   ./docker_files/start-web.sh
   ```

2. Access the web interface at: http://localhost:5000

### Docker Configuration

The Docker environment includes:
- Python 3.9 with all required dependencies
- Automatic handling of environment variables
- Persistent volumes for responses and uploads
- VS Code devcontainer support

For more detailed information about the Docker setup, see `docker_files/README.md`.
