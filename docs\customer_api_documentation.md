# Customer Response Specifications API Documentation

This document provides detailed information about the Customer Response Specifications API endpoints, their parameters, and expected responses.

## Overview

The Customer Response Specifications API allows customers to:
1. Create and manage response specifications for Google Forms
2. Set weights for option-based questions (multiple choice, dropdown, etc.)
3. Provide example responses for open-ended questions (short answer, paragraph)
4. Generate shareable links for specifications
5. Apply specifications to forms for automated response generation

## Base URL

All API endpoints are relative to the base URL of your application.

## Authentication

Currently, the API does not require authentication. Future versions may implement authentication mechanisms.

## API Endpoints

### Customer Specifications Management

#### List All Specifications

```
GET /api/customer/specifications
```

**Response:**
```json
[
  {
    "id": "spec_id",
    "name": "Specification Name",
    "form_id": "form_id",
    "form_title": "Form Title",
    "customer_name": "Customer Name",
    "created_at": 1621234567,
    "last_updated": 1621234567
  },
  ...
]
```

#### Create a New Specification

```
POST /api/customer/specifications
```

**Request Body:**
```json
{
  "form_id": "form_id",
  "name": "Specification Name",
  "customer_name": "Customer Name",
  "description": "Optional description"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Specification created successfully",
  "specification": {
    "id": "spec_id",
    "name": "Specification Name",
    "form_id": "form_id",
    "form_title": "Form Title",
    "customer_name": "Customer Name",
    "description": "Optional description",
    "created_at": 1621234567,
    "weights": {},
    "examples": {},
    "share_token": null
  }
}
```

#### Get a Specification

```
GET /api/customer/specifications/{spec_id}
```

**Response:**
```json
{
  "id": "spec_id",
  "name": "Specification Name",
  "form_id": "form_id",
  "form_title": "Form Title",
  "customer_name": "Customer Name",
  "description": "Optional description",
  "created_at": 1621234567,
  "weights": {
    "question_id": {
      "Option 1": 40,
      "Option 2": 30,
      "Option 3": 20,
      "Option 4": 10
    }
  },
  "examples": {
    "question_id": [
      "Example response 1",
      "Example response 2",
      "Example response 3"
    ]
  },
  "share_token": "token"
}
```

#### Update a Specification

```
PUT /api/customer/specifications/{spec_id}
```

**Request Body:**
```json
{
  "name": "Updated Name",
  "customer_name": "Updated Customer",
  "description": "Updated description"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Specification updated successfully",
  "specification": {
    "id": "spec_id",
    "name": "Updated Name",
    "customer_name": "Updated Customer",
    "description": "Updated description",
    ...
  }
}
```

#### Delete a Specification

```
DELETE /api/customer/specifications/{spec_id}
```

**Response:**
```json
{
  "success": true,
  "message": "Specification deleted successfully"
}
```

### Form Questions Retrieval

#### Get Form Questions for Customer Configuration

```
GET /api/customer/forms/{form_id}/questions
```

**Response:**
```json
{
  "form_id": "form_id",
  "form_title": "Form Title",
  "questions": [
    {
      "id": "question_id",
      "title": "Question Title",
      "type": "short_answer",
      "type_id": 0,
      "category": "open_ended",
      "options": [],
      "required": true
    },
    {
      "id": "question_id",
      "title": "Question Title",
      "type": "multiple_choice",
      "type_id": 2,
      "category": "option_based",
      "options": ["Option 1", "Option 2", "Option 3"],
      "required": true
    },
    ...
  ]
}
```

### Response Weight Management

#### Set Weights for Option-Based Questions

```
POST /api/customer/specifications/{spec_id}/weights
```

**Request Body:**
```json
{
  "question_id": "question_id",
  "weights": {
    "Option 1": 40,
    "Option 2": 30,
    "Option 3": 20,
    "Option 4": 10
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Weights set successfully"
}
```

#### Get Weights for Option-Based Questions

```
GET /api/customer/specifications/{spec_id}/weights/{question_id}
```

**Response:**
```json
{
  "question_id": "question_id",
  "weights": {
    "Option 1": 40,
    "Option 2": 30,
    "Option 3": 20,
    "Option 4": 10
  }
}
```

### Example Response Management

#### Set Example Responses for Open-Ended Questions

```
POST /api/customer/specifications/{spec_id}/examples
```

**Request Body:**
```json
{
  "question_id": "question_id",
  "examples": [
    "Example response 1",
    "Example response 2",
    "Example response 3"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Examples set successfully"
}
```

#### Get Example Responses for Open-Ended Questions

```
GET /api/customer/specifications/{spec_id}/examples/{question_id}
```

**Response:**
```json
{
  "question_id": "question_id",
  "examples": [
    "Example response 1",
    "Example response 2",
    "Example response 3"
  ]
}
```

### Shareable Link Management

#### Generate a Shareable Link

```
POST /api/customer/specifications/{spec_id}/share
```

**Request Body:**
```json
{
  "expires_in_days": 30
}
```

**Response:**
```json
{
  "success": true,
  "message": "Share link generated successfully",
  "token": "token",
  "share_url": "https://example.com/api/customer/share/token",
  "expires_in_days": 30
}
```

#### Access a Shared Specification

```
GET /api/customer/share/{token}
```

**Response:**
```json
{
  "success": true,
  "specification": {
    "id": "spec_id",
    "name": "Specification Name",
    "form_id": "form_id",
    "form_title": "Form Title",
    "customer_name": "Customer Name",
    "description": "Optional description",
    "created_at": 1621234567,
    "weights": {...},
    "examples": {...},
    "share_token": "token"
  }
}
```

### Apply Specification

#### Apply a Specification to a Form

```
POST /api/customer/specifications/{spec_id}/apply
```

**Request Body:**
```json
{
  "total_responses": 100
}
```

**Response:**
```json
{
  "success": true,
  "message": "Specification applied successfully with 100 total responses"
}
```

### Token Management

#### Revoke a Token

```
POST /api/customer/tokens/revoke/{token}
```

**Response:**
```json
{
  "success": true,
  "message": "Token revoked successfully"
}
```

#### Extend a Token's Expiration

```
POST /api/customer/tokens/extend/{token}
```

**Request Body:**
```json
{
  "additional_days": 30
}
```

**Response:**
```json
{
  "success": true,
  "message": "Token expiration extended by 30 days"
}
```

#### Clean Expired Tokens

```
POST /api/customer/tokens/clean
```

**Response:**
```json
{
  "success": true,
  "message": "Cleaned 5 expired tokens"
}
```

## Error Responses

All API endpoints return appropriate HTTP status codes and error messages in case of failure:

```json
{
  "error": "Error message"
}
```

Common error status codes:
- 400: Bad Request - Missing or invalid parameters
- 403: Forbidden - Invalid or expired token
- 404: Not Found - Resource not found
- 500: Internal Server Error - Server-side error

## Weight Conversion

When applying a specification to a form, percentage-based weights are converted to count-based weights:

1. If weights sum to approximately 100 (95-105), they are treated as percentages
2. The system calculates how many responses to generate for each option based on the total responses
3. For example, with total_responses=100:
   - Option 1: 40% → 40 responses
   - Option 2: 30% → 30 responses
   - Option 3: 20% → 20 responses
   - Option 4: 10% → 10 responses

## Integration with Form Submission

After applying a specification to a form, you can use the existing form submission API to submit the form:

```
POST /api/submit/start
```

With the following parameters:
```json
{
  "form_id": "form_id",
  "form_url": "form_url",
  "count": 100,
  "delay_min": 1000,
  "delay_max": 3000,
  "max_workers": 5
}
```

This will submit the form with the responses generated according to the specification.
