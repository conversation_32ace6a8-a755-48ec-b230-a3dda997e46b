# Google Form Autofill and Submit - Folder Structure

## Project Organization

This project has been organized following best practices with a modular structure for better maintainability and scalability.

## Root Directory Structure

```
googleform-autofill-and-submit-main/
├── src/                          # Main source code
│   ├── core/                     # Core application logic
│   ├── managers/                 # Manager classes for various functionalities
│   ├── generators/               # Response generation modules
│   ├── forms/                    # Form handling modules
│   └── utils/                    # Utility functions and helpers
├── config/                       # Configuration files
├── docs/                         # Documentation files
├── scripts/                      # Utility scripts and entry points
├── web/                          # Web interface
├── logs/                         # Application logs
├── responses/                    # Generated responses and history
├── models_cache/                 # Cached AI models
├── examples/                     # Example files
├── ai_providers/                 # AI provider configurations
├── interactive-feedback-mcp/     # Interactive feedback system
├── requirements.txt              # Python dependencies
├── .env                          # Environment variables
├── .gitignore                    # Git ignore rules
└── LICENSE                       # License file
```

## Detailed Structure

### src/ - Source Code
```
src/
├── __init__.py
├── core/                         # Core application components
│   ├── __init__.py
│   ├── main.py                   # Original main entry point
│   ├── enhanced_main.py          # Enhanced main entry point
│   ├── final_review_gate.py      # Final review functionality
│   ├── response_storage.py       # Response storage management
│   ├── submission_history.py     # Submission history tracking
│   ├── submission_queue.py       # Submission queue management
│   └── submission_scheduler.py   # Submission scheduling
├── managers/                     # Manager classes
│   ├── __init__.py
│   ├── billing_manager.py        # Billing and cost management
│   ├── config_manager.py         # Configuration management
│   ├── feedback_manager.py       # Feedback system management
│   ├── gemini_key_manager.py     # Gemini API key management
│   ├── notification_manager.py   # Notification system
│   ├── submission_manager.py     # Form submission management
│   └── token_manager.py          # Token management
├── generators/                   # Response generation
│   ├── __init__.py
│   ├── ai_response_generator.py  # AI-powered response generation
│   ├── enhanced_response_generator.py # Enhanced response generation
│   ├── generator.py              # Base generator
│   └── response_generator.py     # Response generation logic
├── forms/                        # Form handling
│   ├── __init__.py
│   ├── form.py                   # Basic form handling
│   └── form_enhanced.py          # Enhanced form handling
└── utils/                        # Utility functions
    ├── __init__.py
    ├── customer_request.py       # Customer request handling
    ├── customer_specification.py # Customer specification management
    ├── enhanced_commands.py      # Enhanced command processing
    ├── enhanced_gemini_client.py # Enhanced Gemini API client
    ├── gemini_client.py          # Gemini API client
    └── logger.py                 # Logging utilities
```

### config/ - Configuration
```
config/
└── config.json                  # Main configuration file
```

### docs/ - Documentation
```
docs/
├── folder_structure.md          # This file - project structure documentation
├── README.md                    # Main project documentation
├── README_ENHANCED.md           # Enhanced documentation
├── AI_RESPONSE_GENERATOR_README.md # AI response generator documentation
├── customer_api_documentation.md # Customer API documentation
├── GRID_QUESTIONS_ENHANCEMENT.md # Grid questions enhancement guide
└── QUESTION_INDEX_FIX_SUMMARY.md # Question index fix summary
```

### scripts/ - Scripts
```
scripts/
├── run_web.bat                  # Windows batch script to run web interface
├── run_web.py                   # Python script to run web interface
└── setup.py                     # Setup script
```

### web/ - Web Interface
```
web/
├── __init__.py
├── api.py                       # API endpoints
├── app.py                       # Flask application
├── customer_api.py              # Customer-specific API
├── forms.py                     # Web forms
├── routes.py                    # URL routing
├── utils.py                     # Web utilities
├── static/                      # Static files (CSS, JS, images)
│   ├── css/
│   ├── js/
│   └── img/
├── templates/                   # HTML templates
└── uploads/                     # File uploads
```

### Other Directories

- **logs/**: Application log files
- **responses/**: Generated responses and submission history
- **models_cache/**: Cached AI model data
- **examples/**: Example configurations and files
- **ai_providers/**: AI provider specific configurations
- **interactive-feedback-mcp/**: Interactive feedback system with MCP integration

## Key Features by Module

### Core Modules
- **main.py / enhanced_main.py**: Application entry points
- **submission_*.py**: Handle form submission workflow
- **response_storage.py**: Manage response data persistence

### Managers
- **config_manager.py**: Centralized configuration management
- **billing_manager.py**: Cost tracking and billing
- **notification_manager.py**: User notifications and alerts
- **token_manager.py**: API token management

### Generators
- **ai_response_generator.py**: AI-powered response generation
- **enhanced_response_generator.py**: Advanced response generation with multiple AI providers

### Forms
- **form.py / form_enhanced.py**: Google Forms integration and handling

### Utils
- **gemini_client.py**: Google Gemini API integration
- **logger.py**: Centralized logging system
- **customer_*.py**: Customer-specific functionality

## Development Guidelines

1. **Modular Design**: Each module has a specific responsibility
2. **Separation of Concerns**: Core logic, managers, generators, and utilities are separated
3. **Configuration Management**: All configuration is centralized in the config/ directory
4. **Documentation**: Each major component is documented
5. **Testing**: Test files should be organized in a tests/ directory (to be added)
6. **Logging**: Centralized logging through the utils/logger.py module

## Import Path Updates

After reorganization, import statements should be updated to reflect the new structure:

```python
# Old imports
from config_manager import ConfigManager
from gemini_client import GeminiClient

# New imports
from src.managers.config_manager import ConfigManager
from src.utils.gemini_client import GeminiClient
```

## Next Steps

1. Update all import statements in the codebase
2. Update any hardcoded file paths
3. Test all functionality to ensure proper operation
4. Add comprehensive test suite in tests/ directory
5. Update CI/CD configurations if applicable