# Google Form AutoFill and Submit - Folder Structure

## Project Overview
This project provides an automated solution for filling and submitting Google Forms with AI-generated responses.

## Root Directory Structure

```
googleform-autofill-and-submit-main/
├── ai_providers/                    # AI provider configurations and integrations
├── config/                          # Configuration files and settings
├── docs/                           # Documentation files
├── examples/                       # Example scripts and usage demonstrations
├── interactive-feedback-mcp/       # Interactive feedback MCP integration
├── logs/                           # Application logs
├── models_cache/                   # Cached AI models and data
├── responses/                      # Response data and submission history
│   ├── customer_specs/             # Customer specification files
│   ├── submission_history/         # Historical submission records
│   └── tokens/                     # Authentication tokens
├── scripts/                        # Utility and automation scripts
├── src/                           # Main source code (organized)
├── web/                           # Web interface and API
├── .env                           # Environment variables
├── .env.example                   # Environment variables template
├── .gitignore                     # Git ignore rules
├── LICENSE                        # Project license
├── requirements.txt               # Python dependencies
└── folder_structure.md            # This file
```

## Source Code Organization (`src/`)

### Core Modules (`src/core/`)
- `main.py` - Main application entry point with basic form submission
- `enhanced_main.py` - Enhanced main with advanced features
- `response_storage.py` - Response data storage and management
- `submission_history.py` - Submission history tracking
- `submission_queue.py` - Queue management for batch submissions
- `submission_scheduler.py` - Scheduling system for timed submissions

### Form Processing (`src/forms/`)
- `form.py` - Basic Google Form parsing and processing
- `form_enhanced.py` - Enhanced form parser with advanced question types

### Response Generation (`src/generators/`)
- `generator.py` - Basic response generation utilities
- `response_generator.py` - Standard response generation with AI
- `enhanced_response_generator.py` - Advanced response generation with examples
- `ai_response_generator.py` - AI-powered response generation

### Management Systems (`src/managers/`)
- `billing_manager.py` - Billing and usage tracking
- `config_manager.py` - Configuration management
- `feedback_manager.py` - User feedback processing
- `gemini_key_manager.py` - Gemini API key management
- `notification_manager.py` - Notification system
- `submission_manager.py` - Submission process management
- `token_manager.py` - Authentication token management

### Utilities (`src/utils/`)
- `customer_request.py` - Customer request handling
- `customer_specification.py` - Customer specification processing
- `enhanced_commands.py` - Enhanced command-line interface
- `enhanced_gemini_client.py` - Enhanced Gemini AI client
- `gemini_client.py` - Basic Gemini AI client
- `logger.py` - Logging utilities

## Web Interface (`web/`)
- `app.py` - Flask application setup
- `api.py` - REST API endpoints
- `customer_api.py` - Customer-specific API endpoints
- `forms.py` - Web form definitions
- `routes.py` - Web route handlers
- `utils.py` - Web utility functions
- `static/` - Static web assets (CSS, JS, images)
- `templates/` - HTML templates
- `uploads/` - File upload storage

## Scripts (`scripts/`)
- `run_web.py` - Web application launcher

## Import Structure Refactoring

All imports have been updated to use proper module paths:

### Fixed Import Patterns:
- `import form` → `from src.forms import form`
- `import generator` → `from src.generators import generator`
- `import gemini_client` → `from src.utils import gemini_client`
- `import src.forms.form as form` → `from src.forms import form`

### Package Structure:
- All packages have proper `__init__.py` files
- Imports follow the `from src.package.module import Class` pattern
- No relative imports are used for better clarity

## Key Features

### Form Processing
- Support for all Google Form question types
- Enhanced parsing for complex form structures
- Grid questions and checkbox handling
- Email field detection and processing

### Response Generation
- AI-powered response generation using Gemini
- Customer example-based response matching
- Quality scoring and diversity filtering
- Batch generation capabilities

### Submission Management
- Multi-threaded batch submissions
- Progress tracking and cancellation
- Error handling and retry mechanisms
- Submission history and analytics

### Web Interface
- User-friendly form management
- Real-time progress monitoring
- Configuration management
- API endpoints for integration

## Configuration

The project uses environment variables for configuration:
- `GEMINI_API_KEY` - Gemini AI API key
- `FLASK_SECRET_KEY` - Flask session secret
- Database and storage configurations

## Dependencies

Key Python packages:
- `flask` - Web framework
- `google-generativeai` - Gemini AI integration
- `requests` - HTTP client
- `python-dotenv` - Environment variable management

## Usage

1. **Web Interface**: Run `python scripts/run_web.py`
2. **Command Line**: Use modules in `src/core/` for direct execution
3. **API Integration**: Use endpoints defined in `web/api.py`

## Development Notes

- Follow OOP and MVC patterns
- Maintain modular structure for flexibility
- Use proper error handling and logging
- Document all public interfaces
- Test imports after any structural changes

## Recent Updates

### 2024-12-19: Customer Form Enhancement - Advanced Question Types
- Enhanced customer form to support all advanced question types without requiring wizard
- Added configuration interfaces for multiple choice grid, checkbox grid, checkboxes, date, and time questions
- Implemented dynamic form controls for adding/removing combinations and ranges
- Added comprehensive JavaScript functions for form management and data serialization
- Enhanced CSS styling for advanced question type interfaces
- Maintained backward compatibility with existing wizard interface

### 2024-12-19: Grid Questions Enhancement
- Enhanced support for multiple choice grid and checkbox grid questions
- Added comprehensive grid question parsing and response generation
- Improved form loading with better error handling for complex question types
- Added detailed documentation for grid question implementation

### 2024-12-19: Question Index Fix
- Fixed critical issue with question indexing in form processing
- Improved question identification and response mapping
- Enhanced error handling and debugging capabilities
- Added comprehensive testing and validation