{"timestamp": 1748878724.0353694, "models": [{"id": "models/gemini-1.0-pro-vision-latest", "name": "gemini-1.0-pro-vision-latest", "description": "The original Gemini 1.0 Pro Vision model version which was optimized for image understanding. Gemini 1.0 Pro Vision was deprecated on July 12, 2024. Move to a newer Gemini version.", "input_token_limit": 12288, "output_token_limit": 4096, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-pro-vision", "name": "gemini-pro-vision", "description": "The original Gemini 1.0 Pro Vision model version which was optimized for image understanding. Gemini 1.0 Pro Vision was deprecated on July 12, 2024. Move to a newer Gemini version.", "input_token_limit": 12288, "output_token_limit": 4096, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-1.5-pro-latest", "name": "gemini-1.5-pro-latest", "description": "Alias that points to the most recent production (non-experimental) release of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens.", "input_token_limit": 2000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-1.5-pro-001", "name": "gemini-1.5-pro-001", "description": "Stable version of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens, released in May of 2024.", "input_token_limit": 2000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent"]}, {"id": "models/gemini-1.5-pro-002", "name": "gemini-1.5-pro-002", "description": "Stable version of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens, released in September of 2024.", "input_token_limit": 2000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent"]}, {"id": "models/gemini-1.5-pro", "name": "gemini-1.5-pro", "description": "Stable version of Gemini 1.5 Pro, our mid-size multimodal model that supports up to 2 million tokens, released in May of 2024.", "input_token_limit": 2000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-latest", "name": "gemini-1.5-flash-latest", "description": "Alias that points to the most recent production (non-experimental) release of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-001", "name": "gemini-1.5-flash-001", "description": "Stable version of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks, released in May of 2024.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent"]}, {"id": "models/gemini-1.5-flash-001-tuning", "name": "gemini-1.5-flash-001-tuning", "description": "Version of Gemini 1.5 Flash that supports tuning, our fast and versatile multimodal model for scaling across diverse tasks, released in May of 2024.", "input_token_limit": 16384, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createTunedModel"]}, {"id": "models/gemini-1.5-flash", "name": "gemini-1.5-flash", "description": "Alias that points to the most recent stable version of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-002", "name": "gemini-1.5-flash-002", "description": "Stable version of Gemini 1.5 Flash, our fast and versatile multimodal model for scaling across diverse tasks, released in September of 2024.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent"]}, {"id": "models/gemini-1.5-flash-8b", "name": "gemini-1.5-flash-8b", "description": "Stable version of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model, released in October of 2024.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["createCachedContent", "generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-8b-001", "name": "gemini-1.5-flash-8b-001", "description": "Stable version of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model, released in October of 2024.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["createCachedContent", "generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-8b-latest", "name": "gemini-1.5-flash-8b-latest", "description": "Alias that points to the most recent production (non-experimental) release of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model, released in October of 2024.", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["createCachedContent", "generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-8b-exp-0827", "name": "gemini-1.5-flash-8b-exp-0827", "description": "Experimental release (August 27th, 2024) of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model. Replaced by Gemini-1.5-flash-8b-001 (stable).", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-1.5-flash-8b-exp-0924", "name": "gemini-1.5-flash-8b-exp-0924", "description": "Experimental release (September 24th, 2024) of Gemini 1.5 Flash-8B, our smallest and most cost effective Flash model. Replaced by Gemini-1.5-flash-8b-001 (stable).", "input_token_limit": 1000000, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-2.5-pro-exp-03-25", "name": "gemini-2.5-pro-exp-03-25", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.5-pro-preview-03-25", "name": "gemini-2.5-pro-preview-03-25", "description": "Gemini 2.5 Pro Preview 03-25", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.5-flash-preview-04-17", "name": "gemini-2.5-flash-preview-04-17", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.5-flash-preview-05-20", "name": "gemini-2.5-flash-preview-05-20", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.5-flash-preview-04-17-thinking", "name": "gemini-2.5-flash-preview-04-17-thinking", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.5-pro-preview-05-06", "name": "gemini-2.5-pro-preview-05-06", "description": "Preview release (May 6th, 2025) of Gemini 2.5 Pro", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-exp", "name": "gemini-2.0-flash-exp", "description": "Gemini 2.0 Flash Experimental", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "bidiGenerateContent"]}, {"id": "models/gemini-2.0-flash", "name": "gemini-2.0-flash", "description": "Gemini 2.0 Flash", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-001", "name": "gemini-2.0-flash-001", "description": "Stable version of Gemini 2.0 Flash, our fast and versatile multimodal model for scaling across diverse tasks, released in January of 2025.", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-exp-image-generation", "name": "gemini-2.0-flash-exp-image-generation", "description": "Gemini 2.0 Flash (Image Generation) Experimental", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "bidiGenerateContent"]}, {"id": "models/gemini-2.0-flash-lite-001", "name": "gemini-2.0-flash-lite-001", "description": "Stable version of Gemini 2.0 Flash Lite", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-lite", "name": "gemini-2.0-flash-lite", "description": "Gemini 2.0 Flash-Lite", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-preview-image-generation", "name": "gemini-2.0-flash-preview-image-generation", "description": "Gemini 2.0 Flash Preview Image Generation", "input_token_limit": 32768, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens"]}, {"id": "models/gemini-2.0-flash-lite-preview-02-05", "name": "gemini-2.0-flash-lite-preview-02-05", "description": "Preview release (February 5th, 2025) of Gemini 2.0 Flash Lite", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-lite-preview", "name": "gemini-2.0-flash-lite-preview", "description": "Preview release (February 5th, 2025) of Gemini 2.0 Flash Lite", "input_token_limit": 1048576, "output_token_limit": 8192, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-pro-exp", "name": "gemini-2.0-pro-exp", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-pro-exp-02-05", "name": "gemini-2.0-pro-exp-02-05", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-exp-1206", "name": "gemini-exp-1206", "description": "Experimental release (March 25th, 2025) of Gemini 2.5 Pro", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-thinking-exp-01-21", "name": "gemini-2.0-flash-thinking-exp-01-21", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-thinking-exp", "name": "gemini-2.0-flash-thinking-exp", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.0-flash-thinking-exp-1219", "name": "gemini-2.0-flash-thinking-exp-1219", "description": "Preview release (April 17th, 2025) of Gemini 2.5 Flash", "input_token_limit": 1048576, "output_token_limit": 65536, "supported_generation_methods": ["generateContent", "countTokens", "createCachedContent", "batchGenerateContent"]}, {"id": "models/gemini-2.5-flash-preview-tts", "name": "gemini-2.5-flash-preview-tts", "description": "Gemini 2.5 Flash Preview TTS", "input_token_limit": 32768, "output_token_limit": 8192, "supported_generation_methods": ["countTokens", "generateContent"]}, {"id": "models/gemini-2.5-pro-preview-tts", "name": "gemini-2.5-pro-preview-tts", "description": "Gemini 2.5 Pro Preview TTS", "input_token_limit": 65536, "output_token_limit": 65536, "supported_generation_methods": ["countTokens", "generateContent"]}, {"id": "models/gemini-embedding-exp-03-07", "name": "gemini-embedding-exp-03-07", "description": "Obtain a distributed representation of a text.", "input_token_limit": 8192, "output_token_limit": 1, "supported_generation_methods": ["embedContent", "countTextTokens"]}, {"id": "models/gemini-embedding-exp", "name": "gemini-embedding-exp", "description": "Obtain a distributed representation of a text.", "input_token_limit": 8192, "output_token_limit": 1, "supported_generation_methods": ["embedContent", "countTextTokens"]}, {"id": "models/gemini-2.5-flash-preview-native-audio-dialog", "name": "gemini-2.5-flash-preview-native-audio-dialog", "description": "Gemini 2.5 Flash Preview Native Audio Dialog", "input_token_limit": 131072, "output_token_limit": 8192, "supported_generation_methods": ["countTokens", "bidiGenerateContent"]}, {"id": "models/gemini-2.5-flash-preview-native-audio-dialog-rai-v3", "name": "gemini-2.5-flash-preview-native-audio-dialog-rai-v3", "description": "Gemini 2.5 Flash Preview Native Audio Dialog RAI v3", "input_token_limit": 131072, "output_token_limit": 8192, "supported_generation_methods": ["countTokens", "bidiGenerateContent"]}, {"id": "models/gemini-2.5-flash-exp-native-audio-thinking-dialog", "name": "gemini-2.5-flash-exp-native-audio-thinking-dialog", "description": "Gemini 2.5 Flash Exp Native Audio Thinking Dialog", "input_token_limit": 131072, "output_token_limit": 8192, "supported_generation_methods": ["countTokens", "bidiGenerateContent"]}, {"id": "models/gemini-2.0-flash-live-001", "name": "gemini-2.0-flash-live-001", "description": "Gemini 2.0 Flash 001", "input_token_limit": 131072, "output_token_limit": 8192, "supported_generation_methods": ["bidiGenerateContent", "countTokens"]}]}