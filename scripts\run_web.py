"""
Run Script for Google Form AutoFill Web Interface

This script runs the web interface for the Google Form AutoFill tool.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from web.app import app

if __name__ == '__main__':
    print("Starting Google Form AutoFill Web Interface...")
    # Run the application in debug mode if not in production
    debug = os.environ.get('FLASK_ENV', 'development') == 'development'
    print(f"Debug mode: {debug}")
    app.run(debug=debug, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
