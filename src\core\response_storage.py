"""
Response Storage Module for Google Form AutoFill

This module provides functionality for storing, managing, and retrieving
form responses generated for Google Forms.
"""

import json
import os
import time
import re
from typing import Dict, List, Any, Optional, Union


class ResponseStorage:
    """Class for managing form responses"""
    
    def __init__(self, storage_dir: str = "responses"):
        """
        Initialize the response storage
        
        Args:
            storage_dir: Directory to store response files
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
    def _get_form_filename(self, form_id: str) -> str:
        """
        Get the filename for a form's responses
        
        Args:
            form_id: The ID of the form
            
        Returns:
            The filename for the form's responses
        """
        return os.path.join(self.storage_dir, f"{form_id}.json")
        
    def save_form_responses(self, form_id: str, form_data: Dict[str, Any]) -> None:
        """
        Save form responses to storage
        
        Args:
            form_id: The ID of the form
            form_data: The form data to save
        """
        filename = self._get_form_filename(form_id)
        
        # Add metadata
        form_data["_metadata"] = {
            "last_updated": time.time(),
            "version": "1.0"
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(form_data, f, ensure_ascii=False, indent=2)
            
    def load_form_responses(self, form_id: str) -> Optional[Dict[str, Any]]:
        """
        Load form responses from storage
        
        Args:
            form_id: The ID of the form
            
        Returns:
            The form data, or None if not found
        """
        filename = self._get_form_filename(form_id)
        if not os.path.exists(filename):
            return None
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            print(f"Error: Could not parse response file {filename}")
            return None
            
    def delete_form_responses(self, form_id: str) -> bool:
        """
        Delete form responses from storage
        
        Args:
            form_id: The ID of the form
            
        Returns:
            True if successful, False otherwise
        """
        filename = self._get_form_filename(form_id)
        if not os.path.exists(filename):
            return False
            
        try:
            os.remove(filename)
            return True
        except OSError:
            return False
            
    def list_saved_forms(self) -> List[str]:
        """
        List all saved form IDs
        
        Returns:
            List of form IDs
        """
        if not os.path.exists(self.storage_dir):
            return []
            
        return [
            os.path.splitext(f)[0] for f in os.listdir(self.storage_dir)
            if f.endswith('.json')
        ]


class FormResponseManager:
    """Class for managing form responses with CRUD operations"""
    
    def __init__(self, storage: ResponseStorage = None):
        """
        Initialize the form response manager
        
        Args:
            storage: ResponseStorage instance (creates new one if None)
        """
        self.storage = storage or ResponseStorage()
        
    def extract_form_id_from_url(self, form_url: str) -> str:
        """
        Extract form ID from a Google Form URL
        
        Args:
            form_url: The URL of the form
            
        Returns:
            The form ID
        """
        # Try to extract the long unique ID from URLs like /forms/d/e/LONG_ID/viewform
        match = re.search(r'/forms/d/e/([^/]+)/', form_url)
        if match:
            return match.group(1)
        
        # Fallback for older or different URL structures if necessary (e.g. /forms/FORM_ID/...)
        if '/forms/' in form_url:
            parts = form_url.split('/forms/')
            if len(parts) > 1:
                form_id_candidate = parts[1].split('/')[0]
                if len(form_id_candidate) > 5: # Arbitrary length to avoid 'd', 'e'
                    return form_id_candidate
        
        # Final fallback: replace slashes if no other pattern matches
        return form_url.replace('/', '_').replace(':', '_')
        
    def initialize_form_data(self, form_id: str, form_title: str, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Initialize a new form data structure
        
        Args:
            form_id: The ID of the form
            form_title: The title of the form
            questions: List of question data
            
        Returns:
            Initialized form data structure
        """
        return {
            "form_id": form_id,
            "form_title": form_title,
            "questions": questions,
            "responses": {},
            "created_at": time.time()
        }
        
    def save_form_data(self, form_id: str, form_data: Dict[str, Any]) -> None:
        """
        Save form data to storage
        
        Args:
            form_id: The ID of the form
            form_data: The form data to save
        """
        self.storage.save_form_responses(form_id, form_data)
        
    def load_form_data(self, form_id: str) -> Optional[Dict[str, Any]]:
        """
        Load form data from storage
        
        Args:
            form_id: The ID of the form
            
        Returns:
            The form data, or None if not found
        """
        return self.storage.load_form_responses(form_id)
        
    def add_response(self, form_id: str, question_id: Union[str, int], response: Union[str, List[str]], weight: int = 1) -> bool:
        """
        Add a response for a question
        
        Args:
            form_id: The ID of the form
            question_id: The ID of the question (can be string or int)
            response: The response text or list of responses
            weight: The weight of the response
            
        Returns:
            True if successful, False otherwise
        """
        form_data = self.load_form_data(form_id)
        if not form_data:
            print(f"Form data not found for form_id: {form_id}")
            return False
            
        if "responses" not in form_data:
            print("Creating 'responses' dictionary in form data")
            form_data["responses"] = {}
        
        # Convert question_id to string for consistency as JSON keys
        str_question_id = str(question_id)
            
        if str_question_id not in form_data["responses"]:
            print(f"Creating responses array for question_id: {str_question_id}")
            form_data["responses"][str_question_id] = []
            
        # Add the response with its weight
        form_data["responses"][str_question_id].append({
            "text": response,
            "weight": weight
        })
        
        print(f"Saving form data with new response for question_id: {str_question_id}")
        self.save_form_data(form_id, form_data)
        return True
        
    def update_response(self, form_id: str, question_id: str, response_index: int, 
                       new_response: Union[str, List[str]], new_weight: Optional[int] = None) -> bool:
        """
        Update a response for a question
        
        Args:
            form_id: The ID of the form
            question_id: The ID of the question
            response_index: The index of the response to update
            new_response: The new response text or list
            new_weight: The new weight (if None, keeps existing weight)
            
        Returns:
            True if successful, False otherwise
        """
        form_data = self.load_form_data(form_id)
        if not form_data or "responses" not in form_data or question_id not in form_data["responses"]:
            return False
            
        responses = form_data["responses"][question_id]
        if response_index < 0 or response_index >= len(responses):
            return False
            
        # Update the response
        responses[response_index]["text"] = new_response
        if new_weight is not None:
            responses[response_index]["weight"] = new_weight
            
        self.save_form_data(form_id, form_data)
        return True
        
    def delete_response(self, form_id: str, question_id: str, response_index: int) -> bool:
        """
        Delete a response for a question
        
        Args:
            form_id: The ID of the form
            question_id: The ID of the question
            response_index: The index of the response to delete
            
        Returns:
            True if successful, False otherwise
        """
        form_data = self.load_form_data(form_id)
        if not form_data or "responses" not in form_data or question_id not in form_data["responses"]:
            return False
            
        responses = form_data["responses"][question_id]
        if response_index < 0 or response_index >= len(responses):
            return False
            
        # Delete the response
        del responses[response_index]
        
        self.save_form_data(form_id, form_data)
        return True
        
    def get_responses(self, form_id: str, question_id: str) -> List[Dict[str, Any]]:
        """
        Get all responses for a question
        
        Args:
            form_id: The ID of the form
            question_id: The ID of the question
            
        Returns:
            List of responses with weights
        """
        form_data = self.load_form_data(form_id)
        if not form_data or "responses" not in form_data or question_id not in form_data["responses"]:
            return []
            
        return form_data["responses"][question_id]
    
    def batch_delete_responses(self, form_id: str, question_id: str, response_indices: List[int]) -> bool:
        """
        Batch delete multiple responses for a question
        
        Args:
            form_id: The ID of the form
            question_id: The ID of the question
            response_indices: List of response indices to delete
            
        Returns:
            True if successful, False otherwise
        """
        form_data = self.load_form_data(form_id)
        if not form_data or "responses" not in form_data or str(question_id) not in form_data["responses"]:
            return False
        
        # Convert question_id to string for consistency
        str_question_id = str(question_id)
        responses = form_data["responses"][str_question_id]
        
        # Validate all indices are within range
        for index in response_indices:
            if index < 0 or index >= len(responses):
                print(f"Invalid response index: {index}")
                return False
        
        # Sort indices in descending order to delete from end to beginning
        # This prevents index shifting issues
        sorted_indices = sorted(set(response_indices), reverse=True)
        
        # Delete responses
        for index in sorted_indices:
            del responses[index]
        
        self.save_form_data(form_id, form_data)
        print(f"Successfully deleted {len(sorted_indices)} responses for question {question_id}")
        return True
    
    def batch_update_weight(self, form_id: str, question_id: str, response_indices: List[int], new_weight: int) -> bool:
        """
        Batch update weights for multiple responses
        
        Args:
            form_id: The ID of the form
            question_id: The ID of the question
            response_indices: List of response indices to update
            new_weight: The new weight to set for all selected responses
            
        Returns:
            True if successful, False otherwise
        """
        form_data = self.load_form_data(form_id)
        if not form_data or "responses" not in form_data or str(question_id) not in form_data["responses"]:
            return False
        
        # Convert question_id to string for consistency
        str_question_id = str(question_id)
        responses = form_data["responses"][str_question_id]
        
        # Validate weight
        if new_weight < 1:
            print(f"Invalid weight: {new_weight}. Weight must be at least 1.")
            return False
        
        # Validate all indices are within range
        for index in response_indices:
            if index < 0 or index >= len(responses):
                print(f"Invalid response index: {index}")
                return False
        
        # Update weights
        updated_count = 0
        for index in response_indices:
            responses[index]["weight"] = new_weight
            updated_count += 1
        
        self.save_form_data(form_id, form_data)
        print(f"Successfully updated weight to {new_weight} for {updated_count} responses for question {question_id}")
        return True
    
    def delete_form(self, form_id: str) -> bool:
        """
        Delete a form and all its data
        
        Args:
            form_id: The ID of the form
            
        Returns:
            True if successful, False otherwise
        """
        return self.storage.delete_form_responses(form_id)
