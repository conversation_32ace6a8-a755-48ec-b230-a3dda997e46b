"""
Submission History Module for Google Form AutoFill

This module provides functionality for storing, managing, and retrieving
submission history with parameters and results.
"""

import json
import os
import time
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime


class SubmissionHistory:
    """Class for managing submission history"""
    
    def __init__(self, storage_dir: str = "responses/submission_history"):
        """
        Initialize the submission history manager
        
        Args:
            storage_dir: Directory to store history files
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
    def _get_history_filename(self) -> str:
        """Get the filename for submission history"""
        return os.path.join(self.storage_dir, "submission_history.json")
        
    def _load_history(self) -> List[Dict[str, Any]]:
        """Load submission history from storage"""
        filename = self._get_history_filename()
        if not os.path.exists(filename):
            return []
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _save_history(self, history: List[Dict[str, Any]]) -> None:
        """Save submission history to storage"""
        filename = self._get_history_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
            
    def add_submission_record(self, form_id: str, form_url: str, form_title: str,
                             count: int, delay_min: int, delay_max: int, 
                             max_workers: int, result: Dict[str, Any]) -> str:
        """
        Add a new submission record to history
        
        Args:
            form_id: The ID of the form
            form_url: The URL of the form
            form_title: The title of the form
            count: Number of submissions
            delay_min: Minimum delay in milliseconds
            delay_max: Maximum delay in milliseconds
            max_workers: Number of parallel workers
            result: Submission result statistics
            
        Returns:
            The unique ID of the created record
        """
        record_id = str(uuid.uuid4())
        timestamp = time.time()
        
        record = {
            "id": record_id,
            "timestamp": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "form_id": form_id,
            "form_url": form_url,
            "form_title": form_title,
            "parameters": {
                "count": count,
                "delay_min": delay_min,
                "delay_max": delay_max,
                "max_workers": max_workers
            },
            "result": result,
            "status": "completed" if result.get("total", 0) > 0 else "failed"
        }
        
        history = self._load_history()
        history.insert(0, record)  # Add to beginning (newest first)
        
        # Keep only the last 100 records to prevent unlimited growth
        history = history[:100]
        
        self._save_history(history)
        return record_id
        
    def get_history(self, limit: Optional[int] = None, form_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get submission history
        
        Args:
            limit: Maximum number of records to return
            form_id: Filter by specific form ID
            
        Returns:
            List of submission records
        """
        history = self._load_history()
        
        # Filter by form ID if specified
        if form_id:
            history = [record for record in history if record.get("form_id") == form_id]
        
        # Apply limit
        if limit:
            history = history[:limit]
            
        return history
        
    def get_record(self, record_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific submission record
        
        Args:
            record_id: The ID of the record
            
        Returns:
            The submission record or None if not found
        """
        history = self._load_history()
        for record in history:
            if record.get("id") == record_id:
                return record
        return None
        
    def delete_record(self, record_id: str) -> bool:
        """
        Delete a submission record
        
        Args:
            record_id: The ID of the record to delete
            
        Returns:
            True if successful, False otherwise
        """
        history = self._load_history()
        updated_history = [record for record in history if record.get("id") != record_id]
        
        if len(updated_history) < len(history):
            self._save_history(updated_history)
            return True
        return False
        
    def clear_history(self, form_id: Optional[str] = None) -> bool:
        """
        Clear submission history
        
        Args:
            form_id: Clear only records for specific form ID, or all if None
            
        Returns:
            True if successful
        """
        if form_id:
            history = self._load_history()
            filtered_history = [record for record in history if record.get("form_id") != form_id]
            self._save_history(filtered_history)
        else:
            self._save_history([])
        return True
        
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get overall submission statistics
        
        Returns:
            Dictionary with overall statistics
        """
        history = self._load_history()
        
        if not history:
            return {
                "total_submissions": 0,
                "total_successful": 0,
                "total_failed": 0,
                "unique_forms": 0,
                "average_success_rate": 0,
                "last_submission": None
            }
        
        total_submissions = sum(record.get("result", {}).get("total", 0) for record in history)
        total_successful = sum(record.get("result", {}).get("successful", 0) for record in history)
        total_failed = sum(record.get("result", {}).get("failed", 0) for record in history)
        
        unique_forms = len(set(record.get("form_id") for record in history))
        
        success_rates = [
            record.get("result", {}).get("success_rate", 0) 
            for record in history 
            if record.get("result", {}).get("total", 0) > 0
        ]
        average_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0
        
        last_submission = history[0] if history else None
        
        return {
            "total_submissions": total_submissions,
            "total_successful": total_successful,
            "total_failed": total_failed,
            "unique_forms": unique_forms,
            "average_success_rate": average_success_rate,
            "last_submission": last_submission
        } 