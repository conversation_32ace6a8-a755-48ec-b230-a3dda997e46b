"""
AI Response Generator Module for Google Form AutoFill

This module provides a command-line interface for the enhanced AI response generation
system with support for customer-provided examples and specifications.
"""

import argparse
import json
import os
import sys
import time
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dotenv import load_dotenv

from src.forms.form_enhanced import Enhanced<PERSON>ormParser
from src.utils.enhanced_gemini_client import EnhancedGeminiClient
from src.generators.enhanced_response_generator import EnhancedResponseGenerator
from src.managers.feedback_manager import FeedbackManager
from src.core.response_storage import FormResponseManager
from src.utils.enhanced_commands import (
    generate_with_examples,
    batch_generate,
    apply_feedback,
    add_customer_feedback,
    add_expert_feedback,
    get_feedback_summary,
    get_learning_insights
)


def load_default_config() -> Dict[str, Any]:
    """
    Load default configuration from .env file or environment variables

    Returns:
        Dictionary with configuration values
    """
    # Load environment variables from .env file if it exists
    env_path = Path('.env')
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        print("Loaded configuration from .env file")

    # Check for API key
    api_key = os.environ.get("GEMINI_API_KEY")

    # Check for multiple API keys (GEMINI_API_KEY_1, GEMINI_API_KEY_2, etc.)
    api_keys = []
    i = 1
    while True:
        key_name = f"GEMINI_API_KEY_{i}"
        if key_name in os.environ:
            api_keys.append(os.environ[key_name])
            i += 1
        else:
            break

    # If multiple keys found, use them instead of the single key
    if api_keys:
        api_key = api_keys
        print(f"Found {len(api_keys)} API keys in environment variables")

    # Load other configuration values
    config = {
        "api_key": api_key,
        "key_strategy": os.environ.get("GEMINI_KEY_STRATEGY", "round_robin"),
        "form_url": os.environ.get("DEFAULT_FORM_URL"),
        "quality_threshold": float(os.environ.get("QUALITY_THRESHOLD", "0.7")),
        "diversity_factor": float(os.environ.get("DIVERSITY_FACTOR", "0.8")),
        "batch_size": int(os.environ.get("BATCH_SIZE", "10")),
        "total_count": int(os.environ.get("TOTAL_COUNT", "100")),
        "sample_count": int(os.environ.get("SAMPLE_COUNT", "5"))
    }

    return config


def main():
    """Main function"""
    # Load default configuration
    config = load_default_config()

    # Create argument parser
    parser = argparse.ArgumentParser(
        description="AI Response Generator for Google Form AutoFill",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Global arguments
    parser.add_argument("--api-key", default=config["api_key"],
                      help="Gemini API key or comma-separated list of keys")
    parser.add_argument("--key-strategy", choices=["round_robin", "random", "least_used"],
                      default=config["key_strategy"],
                      help="Strategy for API key selection when multiple keys are provided")

    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Generate with examples command
    gen_examples_parser = subparsers.add_parser(
        "generate-with-examples",
        help="Generate responses using customer-provided examples"
    )
    gen_examples_parser.add_argument("form_url", help="Google Form URL")
    gen_examples_parser.add_argument("examples_file", help="Path to JSON file with example responses")
    gen_examples_parser.add_argument("--sample-count", type=int, default=config["sample_count"],
                                   help="Number of samples to generate per question")
    gen_examples_parser.add_argument("--quality-threshold", type=float, default=config["quality_threshold"],
                                   help="Minimum quality score for responses (0.0-1.0)")
    gen_examples_parser.add_argument("--diversity-factor", type=float, default=config["diversity_factor"],
                                   help="Factor controlling response diversity (0.0-1.0)")
    gen_examples_parser.add_argument("--style-guidance", dest="style_guidance_file",
                                   help="Path to JSON file with style guidance")

    # Batch generate command
    batch_gen_parser = subparsers.add_parser(
        "batch-generate",
        help="Generate responses in batches for efficiency"
    )
    batch_gen_parser.add_argument("form_url", help="Google Form URL")
    batch_gen_parser.add_argument("examples_file", help="Path to JSON file with example responses")
    batch_gen_parser.add_argument("--batch-size", type=int, default=config["batch_size"],
                                help="Number of responses to generate in each batch")
    batch_gen_parser.add_argument("--total-count", type=int, default=config["total_count"],
                                help="Total number of responses to generate")
    batch_gen_parser.add_argument("--quality-threshold", type=float, default=config["quality_threshold"],
                                help="Minimum quality score for responses (0.0-1.0)")
    batch_gen_parser.add_argument("--diversity-factor", type=float, default=config["diversity_factor"],
                                help="Factor controlling response diversity (0.0-1.0)")
    batch_gen_parser.add_argument("--style-guidance", dest="style_guidance_file",
                                help="Path to JSON file with style guidance")

    # Apply feedback command
    apply_feedback_parser = subparsers.add_parser(
        "apply-feedback",
        help="Apply stored feedback to adjust response weights"
    )
    apply_feedback_parser.add_argument("form_id", help="Form ID")

    # Add customer feedback command
    add_customer_feedback_parser = subparsers.add_parser(
        "add-customer-feedback",
        help="Add customer feedback from a JSON file"
    )
    add_customer_feedback_parser.add_argument("form_id", help="Form ID")
    add_customer_feedback_parser.add_argument("feedback_file", help="Path to JSON file with feedback data")

    # Add expert feedback command
    add_expert_feedback_parser = subparsers.add_parser(
        "add-expert-feedback",
        help="Add expert feedback from a JSON file"
    )
    add_expert_feedback_parser.add_argument("form_id", help="Form ID")
    add_expert_feedback_parser.add_argument("feedback_file", help="Path to JSON file with feedback data")

    # Get feedback summary command
    get_feedback_summary_parser = subparsers.add_parser(
        "feedback-summary",
        help="Get a summary of feedback for a form"
    )
    get_feedback_summary_parser.add_argument("form_id", help="Form ID")
    get_feedback_summary_parser.add_argument("--output", dest="output_file",
                                          help="Path to save the summary as JSON")

    # Get learning insights command
    get_learning_insights_parser = subparsers.add_parser(
        "learning-insights",
        help="Get insights from feedback for improving future generations"
    )
    get_learning_insights_parser.add_argument("form_id", help="Form ID")
    get_learning_insights_parser.add_argument("--output", dest="output_file",
                                           help="Path to save the insights as JSON")

    # Parse arguments
    args = parser.parse_args()

    # Process API key input (handle comma-separated list)
    if isinstance(args.api_key, str) and ',' in args.api_key:
        args.api_key = [key.strip() for key in args.api_key.split(',') if key.strip()]

    # Execute command
    if args.command == "generate-with-examples":
        result = generate_with_examples(
            args.form_url,
            args.examples_file,
            args.api_key,
            args.sample_count,
            args.quality_threshold,
            args.diversity_factor,
            args.style_guidance_file,
            args.key_strategy
        )
        print(result)

    elif args.command == "batch-generate":
        result = batch_generate(
            args.form_url,
            args.examples_file,
            args.api_key,
            args.batch_size,
            args.total_count,
            args.quality_threshold,
            args.diversity_factor,
            args.style_guidance_file,
            args.key_strategy
        )
        print(result)

    elif args.command == "apply-feedback":
        result = apply_feedback(args.form_id)
        print(result)

    elif args.command == "add-customer-feedback":
        result = add_customer_feedback(args.form_id, args.feedback_file)
        print(result)

    elif args.command == "add-expert-feedback":
        result = add_expert_feedback(args.form_id, args.feedback_file)
        print(result)

    elif args.command == "feedback-summary":
        result = get_feedback_summary(args.form_id, args.output_file)
        print(result)

    elif args.command == "learning-insights":
        result = get_learning_insights(args.form_id, args.output_file)
        print(result)

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
