"""
Feedback Manager Module for Google Form AutoFill

This module provides functionality for collecting, storing, and applying
customer feedback to improve response quality.
"""

import json
import os
import time
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

from src.core.response_storage import FormResponseManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class FeedbackManager:
    """Class for managing customer feedback on generated responses"""

    def __init__(self, storage_dir: str = "feedback"):
        """
        Initialize the feedback manager

        Args:
            storage_dir: Directory to store feedback data
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        self.response_manager = FormResponseManager()

    def _get_feedback_filename(self, form_id: str) -> str:
        """
        Get the filename for a form's feedback data

        Args:
            form_id: The ID of the form

        Returns:
            The filename for the form's feedback data
        """
        return os.path.join(self.storage_dir, f"{form_id}_feedback.json")

    def save_feedback(self, form_id: str, feedback_data: Dict[str, Any]) -> bool:
        """
        Save feedback data to storage

        Args:
            form_id: The ID of the form
            feedback_data: The feedback data to save

        Returns:
            True if successful, False otherwise
        """
        filename = self._get_feedback_filename(form_id)

        # Load existing feedback if available
        existing_feedback = []
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    existing_feedback = json.load(f)
            except json.JSONDecodeError:
                logger.error(f"Error parsing feedback file {filename}")
                existing_feedback = []

        # Add timestamp to feedback
        feedback_data["timestamp"] = time.time()

        # Add to existing feedback
        existing_feedback.append(feedback_data)

        # Save updated feedback
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(existing_feedback, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving feedback: {e}")
            return False

    def load_feedback(self, form_id: str) -> List[Dict[str, Any]]:
        """
        Load feedback data from storage

        Args:
            form_id: The ID of the form

        Returns:
            List of feedback data dictionaries
        """
        filename = self._get_feedback_filename(form_id)
        if not os.path.exists(filename):
            return []

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            logger.error(f"Error parsing feedback file {filename}")
            return []

    def apply_feedback(self, form_id: str) -> bool:
        """
        Apply stored feedback to adjust response weights

        Args:
            form_id: The ID of the form

        Returns:
            True if successful, False otherwise
        """
        # Load form data
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data or "responses" not in form_data:
            logger.error(f"Form data not found for form_id: {form_id}")
            return False

        # Load feedback
        feedback_list = self.load_feedback(form_id)
        if not feedback_list:
            logger.info(f"No feedback found for form_id: {form_id}")
            return False

        # Track changes
        changes_made = False

        # Process each feedback entry
        for feedback in feedback_list:
            # Skip feedback that has already been applied
            if feedback.get("applied", False):
                continue

            # Apply ratings to adjust weights
            if "ratings" in feedback:
                self._adjust_weights_from_ratings(form_id, form_data, feedback["ratings"])
                changes_made = True

            # Apply feedback on specific responses
            if "response_feedback" in feedback:
                self._apply_response_feedback(form_id, form_data, feedback["response_feedback"])
                changes_made = True

            # Mark feedback as applied
            feedback["applied"] = True

        # Save updated form data if changes were made
        if changes_made:
            self.response_manager.save_form_data(form_id, form_data)
            
            # Save updated feedback
            filename = self._get_feedback_filename(form_id)
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(feedback_list, f, ensure_ascii=False, indent=2)

        return changes_made

    def _adjust_weights_from_ratings(self, form_id: str, form_data: Dict[str, Any], 
                                   ratings: Dict[str, Dict[str, float]]) -> None:
        """
        Adjust response weights based on ratings

        Args:
            form_id: The ID of the form
            form_data: The form data
            ratings: Dictionary mapping question IDs to dictionaries of response indices and ratings
        """
        for question_id, question_ratings in ratings.items():
            if question_id not in form_data["responses"]:
                continue

            responses = form_data["responses"][question_id]

            for response_idx_str, rating in question_ratings.items():
                try:
                    response_idx = int(response_idx_str)
                    if 0 <= response_idx < len(responses):
                        # Adjust weight based on rating (scale of 1-5)
                        current_weight = responses[response_idx].get("weight", 1)

                        # Increase weight for high ratings, decrease for low ratings
                        if rating >= 4.0:  # High rating
                            new_weight = current_weight * 1.5
                        elif rating <= 2.0:  # Low rating
                            new_weight = current_weight * 0.5
                        else:  # Neutral rating
                            new_weight = current_weight

                        # Ensure weight is at least 1
                        new_weight = max(int(new_weight), 1)

                        # Update weight
                        responses[response_idx]["weight"] = new_weight
                        
                        # Log the change
                        logger.info(f"Adjusted weight for response {response_idx} in question {question_id}: {current_weight} -> {new_weight}")
                except (ValueError, IndexError):
                    continue

    def _apply_response_feedback(self, form_id: str, form_data: Dict[str, Any],
                               response_feedback: Dict[str, List[Dict[str, Any]]]) -> None:
        """
        Apply detailed feedback on specific responses

        Args:
            form_id: The ID of the form
            form_data: The form data
            response_feedback: Dictionary mapping question IDs to lists of feedback objects
        """
        for question_id, feedback_list in response_feedback.items():
            if question_id not in form_data["responses"]:
                continue

            responses = form_data["responses"][question_id]

            for feedback_item in feedback_list:
                response_idx = feedback_item.get("response_index")
                if response_idx is None or not isinstance(response_idx, int) or response_idx < 0 or response_idx >= len(responses):
                    continue

                # Get current response
                response_obj = responses[response_idx]

                # Apply feedback
                feedback_type = feedback_item.get("type", "")
                
                if feedback_type == "approve":
                    # Increase weight for approved responses
                    current_weight = response_obj.get("weight", 1)
                    new_weight = int(current_weight * 2)
                    response_obj["weight"] = new_weight
                    logger.info(f"Approved response {response_idx} in question {question_id}: weight {current_weight} -> {new_weight}")
                    
                elif feedback_type == "reject":
                    # Decrease weight for rejected responses
                    current_weight = response_obj.get("weight", 1)
                    new_weight = max(int(current_weight * 0.25), 1)
                    response_obj["weight"] = new_weight
                    logger.info(f"Rejected response {response_idx} in question {question_id}: weight {current_weight} -> {new_weight}")
                    
                elif feedback_type == "edit":
                    # Replace response text with edited version
                    if "edited_text" in feedback_item:
                        response_obj["text"] = feedback_item["edited_text"]
                        # Slightly increase weight for manually edited responses
                        current_weight = response_obj.get("weight", 1)
                        response_obj["weight"] = int(current_weight * 1.2)
                        logger.info(f"Edited response {response_idx} in question {question_id}")

    def add_customer_feedback(self, form_id: str, customer_id: str, 
                            ratings: Dict[str, Dict[str, float]],
                            comments: Optional[Dict[str, str]] = None) -> bool:
        """
        Add customer feedback on responses

        Args:
            form_id: The ID of the form
            customer_id: Identifier for the customer
            ratings: Dictionary mapping question IDs to dictionaries of response indices and ratings
            comments: Optional dictionary mapping question IDs to feedback comments

        Returns:
            True if successful, False otherwise
        """
        feedback_data = {
            "customer_id": customer_id,
            "ratings": ratings,
            "comments": comments or {},
            "timestamp": time.time(),
            "applied": False
        }

        return self.save_feedback(form_id, feedback_data)

    def add_expert_feedback(self, form_id: str, expert_id: str,
                          response_feedback: Dict[str, List[Dict[str, Any]]],
                          general_comments: Optional[str] = None) -> bool:
        """
        Add expert feedback on responses with detailed annotations

        Args:
            form_id: The ID of the form
            expert_id: Identifier for the expert reviewer
            response_feedback: Dictionary mapping question IDs to lists of feedback objects
            general_comments: Optional general comments on the responses

        Returns:
            True if successful, False otherwise
        """
        feedback_data = {
            "expert_id": expert_id,
            "response_feedback": response_feedback,
            "general_comments": general_comments,
            "timestamp": time.time(),
            "applied": False
        }

        return self.save_feedback(form_id, feedback_data)

    def get_feedback_summary(self, form_id: str) -> Dict[str, Any]:
        """
        Get a summary of feedback for a form

        Args:
            form_id: The ID of the form

        Returns:
            Dictionary with feedback summary
        """
        feedback_list = self.load_feedback(form_id)
        if not feedback_list:
            return {"form_id": form_id, "feedback_count": 0}

        # Initialize summary
        summary = {
            "form_id": form_id,
            "feedback_count": len(feedback_list),
            "customer_feedback_count": 0,
            "expert_feedback_count": 0,
            "average_ratings": {},
            "question_feedback": {},
            "last_feedback_time": 0
        }

        # Process each feedback entry
        for feedback in feedback_list:
            # Update counts
            if "customer_id" in feedback:
                summary["customer_feedback_count"] += 1
            if "expert_id" in feedback:
                summary["expert_feedback_count"] += 1

            # Update last feedback time
            if feedback.get("timestamp", 0) > summary["last_feedback_time"]:
                summary["last_feedback_time"] = feedback.get("timestamp", 0)

            # Process ratings
            if "ratings" in feedback:
                for question_id, ratings in feedback["ratings"].items():
                    if question_id not in summary["average_ratings"]:
                        summary["average_ratings"][question_id] = {"total": 0, "count": 0}

                    for _, rating in ratings.items():
                        summary["average_ratings"][question_id]["total"] += rating
                        summary["average_ratings"][question_id]["count"] += 1

            # Process response feedback
            if "response_feedback" in feedback:
                for question_id, feedback_items in feedback["response_feedback"].items():
                    if question_id not in summary["question_feedback"]:
                        summary["question_feedback"][question_id] = {
                            "approve_count": 0,
                            "reject_count": 0,
                            "edit_count": 0
                        }

                    for item in feedback_items:
                        feedback_type = item.get("type", "")
                        if feedback_type == "approve":
                            summary["question_feedback"][question_id]["approve_count"] += 1
                        elif feedback_type == "reject":
                            summary["question_feedback"][question_id]["reject_count"] += 1
                        elif feedback_type == "edit":
                            summary["question_feedback"][question_id]["edit_count"] += 1

        # Calculate average ratings
        for question_id, data in summary["average_ratings"].items():
            if data["count"] > 0:
                summary["average_ratings"][question_id] = round(data["total"] / data["count"], 2)
            else:
                summary["average_ratings"][question_id] = 0

        return summary

    def get_learning_insights(self, form_id: str) -> Dict[str, Any]:
        """
        Get insights from feedback for improving future generations

        Args:
            form_id: The ID of the form

        Returns:
            Dictionary with insights
        """
        feedback_list = self.load_feedback(form_id)
        if not feedback_list:
            return {"form_id": form_id, "insights": []}

        # Load form data to get questions
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data:
            return {"form_id": form_id, "insights": []}

        # Initialize insights
        insights = {
            "form_id": form_id,
            "question_insights": {},
            "general_insights": []
        }

        # Get questions
        questions = {}
        if "questions" in form_data:
            for question in form_data["questions"]:
                questions[str(question["id"])] = question["title"]

        # Process each feedback entry
        for feedback in feedback_list:
            # Process ratings
            if "ratings" in feedback:
                for question_id, ratings in feedback["ratings"].items():
                    if question_id not in insights["question_insights"]:
                        insights["question_insights"][question_id] = {
                            "title": questions.get(question_id, f"Question {question_id}"),
                            "high_rated_responses": [],
                            "low_rated_responses": [],
                            "improvement_suggestions": []
                        }

                    # Get responses for this question
                    if "responses" in form_data and question_id in form_data["responses"]:
                        responses = form_data["responses"][question_id]
                        
                        for response_idx_str, rating in ratings.items():
                            try:
                                response_idx = int(response_idx_str)
                                if 0 <= response_idx < len(responses):
                                    response_text = responses[response_idx].get("text", "")
                                    
                                    if rating >= 4.0:  # High rating
                                        insights["question_insights"][question_id]["high_rated_responses"].append(response_text)
                                    elif rating <= 2.0:  # Low rating
                                        insights["question_insights"][question_id]["low_rated_responses"].append(response_text)
                            except (ValueError, IndexError):
                                continue

            # Process comments
            if "comments" in feedback:
                for question_id, comment in feedback["comments"].items():
                    if comment and question_id in insights["question_insights"]:
                        insights["question_insights"][question_id]["improvement_suggestions"].append(comment)

            # Process general comments
            if "general_comments" in feedback and feedback["general_comments"]:
                insights["general_insights"].append(feedback["general_comments"])

        return insights
