"""
Gemini API Key Manager for Google Form AutoFill

This module provides a key manager for Google's Gemini AI model to support
multiple API keys with load balancing and automatic fallback.
"""

import random
import time
from typing import List, Dict, Optional, Set
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GeminiKeyManager:
    """Manager for handling multiple Gemini API keys with load balancing and fallback"""

    def __init__(self, api_keys: List[str], strategy: str = "round_robin"):
        """
        Initialize the Gemini key manager

        Args:
            api_keys: List of Gemini API keys
            strategy: Key selection strategy ("round_robin", "random", or "least_used")
        """
        if not api_keys:
            raise ValueError("At least one API key must be provided")

        self.api_keys = api_keys
        self.strategy = strategy
        self.current_index = 0
        self.failed_keys: Set[str] = set()
        self.key_usage: Dict[str, int] = {key: 0 for key in api_keys}
        self.last_reset_time = time.time()
        self.reset_interval = 3600  # Reset usage stats every hour

    def get_next_key(self) -> str:
        """
        Get the next API key based on the selected strategy

        Returns:
            The next API key to use
        """
        # Check if we need to reset usage statistics
        self._check_reset_usage_stats()

        # If all keys have failed, reset the failed keys set
        if len(self.failed_keys) == len(self.api_keys):
            logger.warning("All API keys have failed. Resetting failed keys list.")
            self.failed_keys.clear()

        # Get available keys (those that haven't failed)
        available_keys = [key for key in self.api_keys if key not in self.failed_keys]
        
        if not available_keys:
            # If no keys are available, use any key
            available_keys = self.api_keys

        # Select key based on strategy
        if self.strategy == "round_robin":
            key = self._round_robin_strategy(available_keys)
        elif self.strategy == "random":
            key = self._random_strategy(available_keys)
        elif self.strategy == "least_used":
            key = self._least_used_strategy(available_keys)
        else:
            # Default to round robin
            key = self._round_robin_strategy(available_keys)

        # Update usage statistics
        self.key_usage[key] += 1
        
        return key

    def _round_robin_strategy(self, available_keys: List[str]) -> str:
        """
        Select a key using round robin strategy

        Args:
            available_keys: List of available API keys

        Returns:
            Selected API key
        """
        # Ensure the current index is within bounds
        self.current_index %= len(self.api_keys)
        
        # Find the next available key
        while self.api_keys[self.current_index] not in available_keys:
            self.current_index = (self.current_index + 1) % len(self.api_keys)
        
        key = self.api_keys[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.api_keys)
        
        return key

    def _random_strategy(self, available_keys: List[str]) -> str:
        """
        Select a key randomly from available keys

        Args:
            available_keys: List of available API keys

        Returns:
            Selected API key
        """
        return random.choice(available_keys)

    def _least_used_strategy(self, available_keys: List[str]) -> str:
        """
        Select the least used key from available keys

        Args:
            available_keys: List of available API keys

        Returns:
            Selected API key
        """
        return min(available_keys, key=lambda k: self.key_usage[k])

    def mark_key_failed(self, key: str) -> None:
        """
        Mark an API key as failed

        Args:
            key: The API key that failed
        """
        if key in self.api_keys:
            logger.warning(f"Marking API key as failed: {key[:5]}...")
            self.failed_keys.add(key)

    def _check_reset_usage_stats(self) -> None:
        """Check if usage statistics should be reset based on time interval"""
        current_time = time.time()
        if current_time - self.last_reset_time > self.reset_interval:
            logger.info("Resetting API key usage statistics")
            self.key_usage = {key: 0 for key in self.api_keys}
            self.last_reset_time = current_time

    def get_key_status(self) -> Dict[str, Dict[str, any]]:
        """
        Get the status of all API keys

        Returns:
            Dictionary with key status information
        """
        return {
            f"key_{i+1}": {
                "active": key not in self.failed_keys,
                "usage_count": self.key_usage[key],
                "preview": key[:5] + "..." + key[-3:] if len(key) > 8 else key
            }
            for i, key in enumerate(self.api_keys)
        }
