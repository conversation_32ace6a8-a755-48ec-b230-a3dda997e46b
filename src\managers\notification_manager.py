"""
Notification Manager Module for Google Form AutoFill

This module provides functionality for sending notifications
about form submissions and other events.
"""

import json
import os
import time
import uuid
import smtplib
import threading
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime


class Notification:
    """Class representing a notification"""
    
    def __init__(self, recipient_id: str, subject: str, message: str, 
                 notification_type: str = "system", reference_id: Optional[str] = None):
        """
        Initialize a notification
        
        Args:
            recipient_id: The ID of the recipient (user or customer)
            subject: The notification subject
            message: The notification message
            notification_type: Type of notification (system, submission, billing, etc.)
            reference_id: Optional ID of the referenced object (request, submission, etc.)
        """
        self.id = str(uuid.uuid4())
        self.recipient_id = recipient_id
        self.subject = subject
        self.message = message
        self.notification_type = notification_type
        self.reference_id = reference_id
        self.created_at = time.time()
        self.read = False
        self.read_at = None
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the notification to a dictionary for storage"""
        return {
            "id": self.id,
            "recipient_id": self.recipient_id,
            "subject": self.subject,
            "message": self.message,
            "notification_type": self.notification_type,
            "reference_id": self.reference_id,
            "created_at": self.created_at,
            "read": self.read,
            "read_at": self.read_at
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Notification':
        """Create a Notification instance from a dictionary"""
        notification = cls(
            recipient_id=data.get("recipient_id", ""),
            subject=data.get("subject", ""),
            message=data.get("message", ""),
            notification_type=data.get("notification_type", "system"),
            reference_id=data.get("reference_id")
        )
        
        # Set additional attributes
        notification.id = data.get("id", notification.id)
        notification.created_at = data.get("created_at", notification.created_at)
        notification.read = data.get("read", False)
        notification.read_at = data.get("read_at")
        
        return notification


class EmailConfig:
    """Class for email configuration"""
    
    def __init__(self, smtp_server: str, smtp_port: int, 
                 username: str, password: str, 
                 sender_email: str, sender_name: Optional[str] = None):
        """
        Initialize email configuration
        
        Args:
            smtp_server: SMTP server address
            smtp_port: SMTP server port
            username: SMTP username
            password: SMTP password
            sender_email: Sender email address
            sender_name: Optional sender name
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.sender_email = sender_email
        self.sender_name = sender_name or sender_email
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the email configuration to a dictionary for storage"""
        return {
            "smtp_server": self.smtp_server,
            "smtp_port": self.smtp_port,
            "username": self.username,
            "password": self.password,
            "sender_email": self.sender_email,
            "sender_name": self.sender_name
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EmailConfig':
        """Create an EmailConfig instance from a dictionary"""
        return cls(
            smtp_server=data.get("smtp_server", ""),
            smtp_port=data.get("smtp_port", 587),
            username=data.get("username", ""),
            password=data.get("password", ""),
            sender_email=data.get("sender_email", ""),
            sender_name=data.get("sender_name")
        )


class NotificationManager:
    """Class for managing notifications"""
    
    def __init__(self, storage_dir: str = "responses/notifications"):
        """
        Initialize the notification manager
        
        Args:
            storage_dir: Directory to store notification data
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
        self.email_config = None
        self._load_email_config()
        
    def _get_notifications_filename(self) -> str:
        """Get the filename for all notifications"""
        return os.path.join(self.storage_dir, "notifications.json")
        
    def _get_recipient_notifications_filename(self, recipient_id: str) -> str:
        """Get the filename for a recipient's notifications"""
        return os.path.join(self.storage_dir, f"{recipient_id}_notifications.json")
        
    def _get_email_config_filename(self) -> str:
        """Get the filename for email configuration"""
        return os.path.join(self.storage_dir, "email_config.json")
        
    def _load_notifications(self) -> List[Dict[str, Any]]:
        """Load all notifications from storage"""
        filename = self._get_notifications_filename()
        if not os.path.exists(filename):
            return []
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _load_recipient_notifications(self, recipient_id: str) -> List[Dict[str, Any]]:
        """Load notifications for a specific recipient"""
        filename = self._get_recipient_notifications_filename(recipient_id)
        if not os.path.exists(filename):
            return []
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _save_notifications(self, notifications: List[Dict[str, Any]]) -> None:
        """Save all notifications to storage"""
        filename = self._get_notifications_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(notifications, f, ensure_ascii=False, indent=2)
            
    def _save_recipient_notifications(self, recipient_id: str, 
                                     notifications: List[Dict[str, Any]]) -> None:
        """Save notifications for a specific recipient"""
        filename = self._get_recipient_notifications_filename(recipient_id)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(notifications, f, ensure_ascii=False, indent=2)
            
    def _load_email_config(self) -> None:
        """Load email configuration from storage"""
        filename = self._get_email_config_filename()
        if not os.path.exists(filename):
            self.email_config = None
            return
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                self.email_config = EmailConfig.from_dict(config_data)
        except (json.JSONDecodeError, FileNotFoundError):
            self.email_config = None
            
    def _save_email_config(self) -> None:
        """Save email configuration to storage"""
        if not self.email_config:
            return
            
        filename = self._get_email_config_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.email_config.to_dict(), f, ensure_ascii=False, indent=2)
            
    def set_email_config(self, config: EmailConfig) -> None:
        """
        Set email configuration
        
        Args:
            config: The EmailConfig object
        """
        self.email_config = config
        self._save_email_config()
        
    def create_notification(self, notification: Notification) -> str:
        """
        Create a new notification
        
        Args:
            notification: The Notification object
            
        Returns:
            The ID of the created notification
        """
        # Save to global notifications
        all_notifications = self._load_notifications()
        all_notifications.append(notification.to_dict())
        self._save_notifications(all_notifications)
        
        # Save to recipient-specific notifications
        recipient_notifications = self._load_recipient_notifications(notification.recipient_id)
        recipient_notifications.append(notification.to_dict())
        self._save_recipient_notifications(notification.recipient_id, recipient_notifications)
        
        return notification.id
        
    def get_notification(self, notification_id: str) -> Optional[Notification]:
        """
        Get a notification by ID
        
        Args:
            notification_id: The ID of the notification
            
        Returns:
            The Notification object, or None if not found
        """
        all_notifications = self._load_notifications()
        for notification_data in all_notifications:
            if notification_data.get("id") == notification_id:
                return Notification.from_dict(notification_data)
        return None
        
    def mark_as_read(self, notification_id: str) -> bool:
        """
        Mark a notification as read
        
        Args:
            notification_id: The ID of the notification
            
        Returns:
            True if successful, False otherwise
        """
        notification = self.get_notification(notification_id)
        if not notification:
            return False
            
        # Update notification
        notification.read = True
        notification.read_at = time.time()
        
        # Update in global notifications
        all_notifications = self._load_notifications()
        updated_global = False
        
        for i, notification_data in enumerate(all_notifications):
            if notification_data.get("id") == notification_id:
                all_notifications[i] = notification.to_dict()
                updated_global = True
                break
                
        if updated_global:
            self._save_notifications(all_notifications)
            
        # Update in recipient-specific notifications
        recipient_notifications = self._load_recipient_notifications(notification.recipient_id)
        updated_recipient = False
        
        for i, notification_data in enumerate(recipient_notifications):
            if notification_data.get("id") == notification_id:
                recipient_notifications[i] = notification.to_dict()
                updated_recipient = True
                break
                
        if updated_recipient:
            self._save_recipient_notifications(notification.recipient_id, recipient_notifications)
            
        return updated_global and updated_recipient
        
    def delete_notification(self, notification_id: str) -> bool:
        """
        Delete a notification
        
        Args:
            notification_id: The ID of the notification
            
        Returns:
            True if successful, False otherwise
        """
        notification = self.get_notification(notification_id)
        if not notification:
            return False
            
        # Remove from global notifications
        all_notifications = self._load_notifications()
        all_notifications = [n for n in all_notifications if n.get("id") != notification_id]
        self._save_notifications(all_notifications)
        
        # Remove from recipient-specific notifications
        recipient_notifications = self._load_recipient_notifications(notification.recipient_id)
        recipient_notifications = [n for n in recipient_notifications if n.get("id") != notification_id]
        self._save_recipient_notifications(notification.recipient_id, recipient_notifications)
        
        return True
        
    def get_recipient_notifications(self, recipient_id: str, 
                                   unread_only: bool = False,
                                   notification_type: Optional[str] = None,
                                   limit: Optional[int] = None) -> List[Notification]:
        """
        Get notifications for a specific recipient
        
        Args:
            recipient_id: The ID of the recipient
            unread_only: Only return unread notifications if True
            notification_type: Optional filter by notification type
            limit: Optional maximum number of notifications to return
            
        Returns:
            List of Notification objects
        """
        notifications = self._load_recipient_notifications(recipient_id)
        
        # Apply filters
        if unread_only:
            notifications = [n for n in notifications if not n.get("read", False)]
            
        if notification_type:
            notifications = [n for n in notifications if n.get("notification_type") == notification_type]
            
        # Sort by creation time (newest first)
        notifications = sorted(notifications, key=lambda n: n.get("created_at", 0), reverse=True)
        
        # Apply limit
        if limit:
            notifications = notifications[:limit]
            
        # Convert to Notification objects
        return [Notification.from_dict(n) for n in notifications]
        
    def get_unread_count(self, recipient_id: str) -> int:
        """
        Get count of unread notifications for a recipient
        
        Args:
            recipient_id: The ID of the recipient
            
        Returns:
            Number of unread notifications
        """
        notifications = self._load_recipient_notifications(recipient_id)
        return sum(1 for n in notifications if not n.get("read", False))
        
    def send_email_notification(self, recipient_email: str, subject: str, 
                               message: str, html_message: Optional[str] = None) -> bool:
        """
        Send an email notification
        
        Args:
            recipient_email: The recipient's email address
            subject: The email subject
            message: The plain text message
            html_message: Optional HTML message
            
        Returns:
            True if successful, False otherwise
        """
        if not self.email_config:
            return False
            
        try:
            # Create message
            email = MIMEMultipart("alternative")
            email["Subject"] = subject
            email["From"] = f"{self.email_config.sender_name} <{self.email_config.sender_email}>"
            email["To"] = recipient_email
            
            # Add plain text part
            email.attach(MIMEText(message, "plain"))
            
            # Add HTML part if provided
            if html_message:
                email.attach(MIMEText(html_message, "html"))
                
            # Connect to SMTP server
            with smtplib.SMTP(self.email_config.smtp_server, self.email_config.smtp_port) as server:
                server.starttls()
                server.login(self.email_config.username, self.email_config.password)
                server.send_message(email)
                
            return True
            
        except Exception as e:
            print(f"Error sending email: {e}")
            return False
            
    def send_submission_notification(self, recipient_id: str, recipient_email: str,
                                    form_title: str, submission_result: Dict[str, Any],
                                    request_id: Optional[str] = None) -> str:
        """
        Send a notification about form submission results
        
        Args:
            recipient_id: The ID of the recipient
            recipient_email: The recipient's email address
            form_title: The title of the form
            submission_result: The submission result data
            request_id: Optional ID of the associated request
            
        Returns:
            The ID of the created notification
        """
        # Create notification subject and message
        subject = f"Form Submission Complete: {form_title}"
        
        successful = submission_result.get("successful", 0)
        failed = submission_result.get("failed", 0)
        total = submission_result.get("total", 0)
        
        message = f"Your form submission for '{form_title}' has completed.\n\n"
        message += f"Results:\n"
        message += f"- Successful submissions: {successful}\n"
        message += f"- Failed submissions: {failed}\n"
        message += f"- Total submissions: {total}\n"
        
        if "error" in submission_result:
            message += f"\nError: {submission_result['error']}\n"
            
        # Create HTML message
        html_message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4a6ee0; color: white; padding: 10px 20px; border-radius: 5px 5px 0 0; }}
                .content {{ padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px; }}
                .success {{ color: #28a745; }}
                .error {{ color: #dc3545; }}
                .total {{ font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>Form Submission Complete</h2>
                </div>
                <div class="content">
                    <p>Your form submission for <strong>{form_title}</strong> has completed.</p>
                    
                    <h3>Results:</h3>
                    <p class="success">Successful submissions: {successful}</p>
                    <p class="error">Failed submissions: {failed}</p>
                    <p class="total">Total submissions: {total}</p>
                    
                    {"<p class='error'><strong>Error:</strong> " + submission_result.get("error", "") + "</p>" if "error" in submission_result else ""}
                    
                    <p>Thank you for using our service!</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Create in-app notification
        notification = Notification(
            recipient_id=recipient_id,
            subject=subject,
            message=message,
            notification_type="submission",
            reference_id=request_id
        )
        
        notification_id = self.create_notification(notification)
        
        # Send email notification if email config is available
        if self.email_config:
            # Send email in a separate thread to avoid blocking
            threading.Thread(
                target=self.send_email_notification,
                args=(recipient_email, subject, message, html_message),
                daemon=True
            ).start()
            
        return notification_id
        
    def send_billing_notification(self, recipient_id: str, recipient_email: str,
                                 billing_record_id: str, amount: float,
                                 submission_count: int, description: str) -> str:
        """
        Send a notification about a new billing record
        
        Args:
            recipient_id: The ID of the recipient
            recipient_email: The recipient's email address
            billing_record_id: The ID of the billing record
            amount: The billing amount
            submission_count: Number of submissions billed
            description: Description of the billing
            
        Returns:
            The ID of the created notification
        """
        # Create notification subject and message
        subject = f"New Billing Record: ${amount:.2f}"
        
        message = f"A new billing record has been created for your account.\n\n"
        message += f"Details:\n"
        message += f"- Amount: ${amount:.2f}\n"
        message += f"- Submissions: {submission_count}\n"
        message += f"- Description: {description}\n"
        message += f"- Billing ID: {billing_record_id}\n"
        
        # Create HTML message
        html_message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4a6ee0; color: white; padding: 10px 20px; border-radius: 5px 5px 0 0; }}
                .content {{ padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px; }}
                .amount {{ font-size: 24px; font-weight: bold; color: #28a745; }}
                .details {{ margin-top: 20px; }}
                .details-row {{ display: flex; margin-bottom: 10px; }}
                .details-label {{ width: 120px; font-weight: bold; }}
                .details-value {{ flex: 1; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>New Billing Record</h2>
                </div>
                <div class="content">
                    <p>A new billing record has been created for your account.</p>
                    
                    <p class="amount">${amount:.2f}</p>
                    
                    <div class="details">
                        <div class="details-row">
                            <div class="details-label">Submissions:</div>
                            <div class="details-value">{submission_count}</div>
                        </div>
                        <div class="details-row">
                            <div class="details-label">Description:</div>
                            <div class="details-value">{description}</div>
                        </div>
                        <div class="details-row">
                            <div class="details-label">Billing ID:</div>
                            <div class="details-value">{billing_record_id}</div>
                        </div>
                    </div>
                    
                    <p>Thank you for using our service!</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Create in-app notification
        notification = Notification(
            recipient_id=recipient_id,
            subject=subject,
            message=message,
            notification_type="billing",
            reference_id=billing_record_id
        )
        
        notification_id = self.create_notification(notification)
        
        # Send email notification if email config is available
        if self.email_config:
            # Send email in a separate thread to avoid blocking
            threading.Thread(
                target=self.send_email_notification,
                args=(recipient_email, subject, message, html_message),
                daemon=True
            ).start()
            
        return notification_id
