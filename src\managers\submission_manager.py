"""
Submission Manager Module for Google Form AutoFill

This module handles the submission of form responses with progress tracking
and error handling.
"""

import json
import random
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Optional, Union, Callable

import requests

from src.forms import form
from src.core.response_storage import FormResponseManager


class SubmissionManager:
    """Class for managing form submissions"""

    def __init__(self):
        """Initialize the submission manager"""
        self.response_manager = FormResponseManager()
        self.current_progress = 0
        self.total_submissions = 0
        self.successful_submissions = 0
        self.failed_submissions = 0
        self._cancelled = False
        self._lock = threading.Lock()
        self._used_responses = {}  # Track used responses for questions with no duplication mode

    def cancel(self):
        """Cancel the current submission process"""
        self._cancelled = True

    def is_cancelled(self):
        """Check if the submission process is cancelled"""
        return self._cancelled

    def reset_cancel_flag(self):
        """Reset the cancellation flag for new submissions"""
        self._cancelled = False

    def reset_used_responses(self):
        """Reset the used responses tracker for questions with no duplication mode"""
        self._used_responses = {}

    def prepare_submission_data(self, form_id: str, question_id: str) -> List[Dict[str, Any]]:
        """
        Prepare submission data for a specific question based on stored responses

        Args:
            form_id: The ID of the form
            question_id: The ID of the question

        Returns:
            List of response data with weights
        """
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data or "responses" not in form_data or str(question_id) not in form_data["responses"]:
            return []

        return form_data["responses"][str(question_id)]

    def select_weighted_response(self, responses: List[Dict[str, Any]]) -> Any:
        """
        Select a response based on weightage

        Args:
            responses: List of response objects with weights

        Returns:
            Selected response text
        """
        if not responses:
            return None

        # Create a list of responses with repetitions based on weight
        weighted_list = []
        for response in responses:
            text = response["text"]
            weight = response["weight"]
            weighted_list.extend([text] * weight)

        if not weighted_list:
            return None

        # Select a random response from the weighted list
        return random.choice(weighted_list)

    def select_no_duplication_response(self, responses: List[Dict[str, Any]], question_id: str) -> Any:
        """
        Select a response without duplication (each response used only once)

        Args:
            responses: List of response objects with weights
            question_id: The ID of the question

        Returns:
            Selected response text or None if all responses have been used
        """
        if not responses:
            return None

        # Get the list of used responses for this question
        used_responses = self._used_responses.get(question_id, set())

        # Find unused responses
        available_responses = [resp for resp in responses if resp["text"] not in used_responses]

        if not available_responses:
            # All responses have been used
            return None

        # Select a random response from available ones
        selected_response = random.choice(available_responses)
        selected_text = selected_response["text"]

        # Mark this response as used
        if question_id not in self._used_responses:
            self._used_responses[question_id] = set()
        self._used_responses[question_id].add(selected_text)

        return selected_text

    def validate_form_structure(self, form_id: str, form_url: str) -> bool:
        """
        Validate that the stored form structure matches the current form

        Args:
            form_id: The ID of the form
            form_url: The URL of the form

        Returns:
            True if form structure is valid, False otherwise
        """
        try:
            # Get current form structure
            current_entries = form.parse_form_entries(form_url)
            if not current_entries:
                print("⚠️ Warning: Could not parse current form structure")
                return False

            # Get stored form structure
            form_data = self.response_manager.load_form_data(form_id)
            if not form_data:
                print("⚠️ Warning: No stored form data found")
                return False

            stored_questions = form_data.get("questions", [])

            # Compare entry IDs
            current_ids = {str(entry["id"]) for entry in current_entries}
            stored_ids = {str(q["id"]) for q in stored_questions}

            missing_ids = stored_ids - current_ids
            new_ids = current_ids - stored_ids

            if missing_ids:
                print(f"⚠️ Warning: Stored form has entry IDs not found in current form: {missing_ids}")
                return False

            if new_ids:
                print(f"📝 Info: Current form has new entry IDs: {new_ids}")

            print("✅ Form structure validation passed")
            return True

        except Exception as e:
            print(f"⚠️ Warning: Form validation failed: {e}")
            return False

    def prepare_form_payload(self, form_id: str, form_url: str) -> Dict[str, Any]:
        """
        Prepare a complete form payload for submission

        Args:
            form_id: The ID of the form
            form_url: The URL of the form

        Returns:
            Form payload dictionary
        """
        form_data = self.response_manager.load_form_data(form_id)
        if not form_data:
            raise ValueError(f"No data found for form ID: {form_id}")

        # Validate form structure before preparing payload
        if not self.validate_form_structure(form_id, form_url):
            print("⚠️ Warning: Form structure validation failed, but continuing with stored data")

        payload = {}

        # Process each question
        for question in form_data["questions"]:
            question_id = question["id"]

            # Skip questions with default values
            if "default_value" in question:
                continue

            # Get responses for this question
            responses = self.prepare_submission_data(form_id, str(question_id))

            # Check if this question has no duplication mode enabled
            question_no_duplication = question.get('no_duplication', False)

            # Select a response based on the question's mode
            if question_no_duplication:
                selected_response = self.select_no_duplication_response(responses, str(question_id))
            else:
                selected_response = self.select_weighted_response(responses)

            # If no response is available, generate a default
            if selected_response is None:
                if question.get("required", False):
                    if question.get("options"):
                        selected_response = random.choice(question["options"])
                    elif question.get("type") == "email":
                        selected_response = "<EMAIL>"
                    else:
                        # Generate a more meaningful default response
                        question_title = question.get("title", "").lower()
                        if "name" in question_title:
                            selected_response = f"User_{random.randint(1000, 9999)}"
                        elif "email" in question_title:
                            selected_response = f"user{random.randint(100, 999)}@example.com"
                        elif "phone" in question_title or "number" in question_title:
                            selected_response = f"555-{random.randint(1000, 9999)}"
                        elif "age" in question_title:
                            selected_response = str(random.randint(18, 65))
                        else:
                            selected_response = f"Response_{random.randint(100, 999)}"
                else:
                    # Skip non-required questions without responses
                    continue

            # Add to payload with proper formatting based on question type
            if question_id == "emailAddress":
                payload["emailAddress"] = selected_response
            else:
                # Handle different response formats based on question type
                question_type = question.get("type", 0)

                if question_type == 4:  # Checkbox question
                    # Parse checkbox response format: "12: Option 1, Option 3" -> ["Option 1", "Option 3"]
                    formatted_response = self._format_checkbox_response(selected_response)
                    payload[f"entry.{question_id}"] = formatted_response
                else:
                    payload[f"entry.{question_id}"] = selected_response

        return payload

    def _format_checkbox_response(self, response: str) -> list:
        """
        Format checkbox response for Google Forms submission

        Args:
            response: Response in format "12: Option 1, Option 3" or "Option 1"

        Returns:
            List of selected options for submission
        """
        if not isinstance(response, str):
            return [str(response)]

        # If response contains ":", it's in the format "12: Option 1, Option 3"
        if ':' in response:
            parts = response.split(':', 1)
            if len(parts) == 2:
                options_str = parts[1].strip()
                # Return all selected options as a list
                options = [opt.strip() for opt in options_str.split(',') if opt.strip()]
                return options if options else [response]

        # If it's a single option or comma-separated options without ":"
        if ',' in response:
            options = [opt.strip() for opt in response.split(',') if opt.strip()]
            return options if options else [response]

        # Return single option as a list
        return [response]

    def submit_form(self, form_url: str, payload: Dict[str, Any], retry_count: int = 3) -> bool:
        """
        Submit a form with the given payload

        Args:
            form_url: The URL of the form
            payload: The form data to submit
            retry_count: Number of retry attempts

        Returns:
            True if submission was successful, False otherwise
        """
        response_url = form.get_form_response_url(form_url)

        for attempt in range(retry_count):
            try:
                response = requests.post(response_url, data=payload, timeout=10)

                if response.status_code == 200:
                    return True

                # If we get a redirect, it might be a success (Google Forms often redirects after submission)
                if response.status_code in [301, 302, 303, 307, 308]:
                    return True

                # Enhanced error logging for 400 errors
                if response.status_code == 400:
                    print(f"\n🚨 400 Bad Request Error - Attempt {attempt + 1}/{retry_count}")
                    print(f"📤 Submitted to: {response_url}")
                    print(f"📦 Payload: {payload}")
                    print(f"📄 Response content: {response.text[:500]}...")

                    # Analyze the error content
                    response_text = response.text.lower()
                    if "entry" in response_text:
                        print("💡 Error likely caused by invalid entry IDs - form structure may have changed")
                    if "required" in response_text:
                        print("💡 Error likely caused by missing required fields")
                    if "invalid" in response_text:
                        print("💡 Error likely caused by invalid form data format")
                else:
                    print(f"Submission failed with status code: {response.status_code}")
                    if response.status_code >= 500:
                        print(f"Server error response: {response.text[:200]}...")

                # Wait before retrying
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

            except Exception as e:
                print(f"Error during submission: {e}")

                # Wait before retrying
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        return False

    def _submit_single(self, form_id: str, form_url: str, delay_range_ms: tuple, submission_index: int) -> bool:
        """
        Submit a single form (for use in thread pool)

        Args:
            form_id: The ID of the form
            form_url: The URL of the form
            delay_range_ms: Range of delay between submissions in milliseconds (min, max)
            submission_index: Index of this submission

        Returns:
            True if successful, False otherwise
        """
        if self.is_cancelled():
            return False

        try:
            # Add random delay before submission (convert ms to seconds)
            delay_seconds = random.uniform(delay_range_ms[0] / 1000, delay_range_ms[1] / 1000)
            time.sleep(delay_seconds)

            if self.is_cancelled():
                return False

            # Prepare payload
            payload = self.prepare_form_payload(form_id, form_url)

            # Submit form
            success = self.submit_form(form_url, payload)

            # Thread-safe progress update
            with self._lock:
                if success:
                    self.successful_submissions += 1
                else:
                    self.failed_submissions += 1

                completed = self.successful_submissions + self.failed_submissions
                self.current_progress = completed / self.total_submissions

            return success

        except Exception as e:
            print(f"Error in submission {submission_index + 1}: {e}")
            with self._lock:
                self.failed_submissions += 1
                completed = self.successful_submissions + self.failed_submissions
                self.current_progress = completed / self.total_submissions
            return False

    def batch_submit(self, form_id: str, form_url: str, count: int,
                    delay_range: tuple = (1000, 3000), max_workers: int = 5,
                    progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Submit multiple form responses in batch using multithreading

        Args:
            form_id: The ID of the form
            form_url: The URL of the form
            count: Number of submissions to make
            delay_range: Range of delay between submissions in milliseconds (min, max)
            max_workers: Maximum number of worker threads
            progress_callback: Optional callback function for progress updates

        Returns:
            Dictionary with submission statistics
        """
        # Reset cancellation flag at the start of new submission
        self.reset_cancel_flag()

        # Reset used responses for no duplication mode
        self.reset_used_responses()

        # Check for per-question no duplication mode constraints
        form_data = self.response_manager.load_form_data(form_id)
        if form_data:
            # Validate that we have enough responses for questions with no duplication enabled
            min_responses = float('inf')
            limited_questions = []

            for question in form_data.get("questions", []):
                # Check if this specific question has no duplication mode enabled
                if question.get('no_duplication', False) and question.get("required", False) and "default_value" not in question:
                    question_id = str(question["id"])
                    responses = form_data.get('responses', {}).get(question_id, [])
                    response_count = len(responses)

                    if response_count > 0:
                        min_responses = min(min_responses, response_count)
                        limited_questions.append({
                            'title': question.get('title', 'Untitled'),
                            'count': response_count,
                            'no_duplication': True
                        })

            # If we have questions with no duplication mode, check the constraint
            if limited_questions and min_responses < count:
                error_msg = f"Cannot submit {count} forms. "
                error_msg += f"Question(s) with No Duplication mode have limited responses: "
                min_questions = [q for q in limited_questions if q['count'] == min_responses]
                error_msg += ", ".join([f"{q['title']} ({q['count']} unique responses)" for q in min_questions[:3]])
                if len(min_questions) > 3:
                    error_msg += f" and {len(min_questions) - 3} more"

                return {
                    "total": 0,
                    "successful": 0,
                    "failed": count,
                    "success_rate": 0,
                    "cancelled": False,
                    "error": error_msg
                }

        self.current_progress = 0
        self.total_submissions = count
        self.successful_submissions = 0
        self.failed_submissions = 0

        # Validate delay range (convert from milliseconds to check)
        delay_min_ms, delay_max_ms = delay_range
        if delay_min_ms > delay_max_ms:
            delay_min_ms, delay_max_ms = delay_max_ms, delay_min_ms

        print(f"Starting batch submission: {count} submissions with {max_workers} threads")
        print(f"Delay range: {delay_min_ms}-{delay_max_ms}ms")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            futures = []
            for i in range(count):
                if self.is_cancelled():
                    break

                future = executor.submit(
                    self._submit_single,
                    form_id,
                    form_url,
                    (delay_min_ms, delay_max_ms),
                    i
                )
                futures.append(future)

            # Process completed tasks
            for future in as_completed(futures):
                if self.is_cancelled():
                    # Cancel remaining futures
                    for f in futures:
                        f.cancel()
                    break

                try:
                    result = future.result()

                    # Call progress callback if provided
                    if progress_callback:
                        progress_callback(self.current_progress, self.successful_submissions, self.failed_submissions)

                except Exception as e:
                    print(f"Error processing submission result: {e}")
                    with self._lock:
                        self.failed_submissions += 1
                        completed = self.successful_submissions + self.failed_submissions
                        self.current_progress = completed / self.total_submissions

                    if progress_callback:
                        progress_callback(self.current_progress, self.successful_submissions, self.failed_submissions)

        # Return statistics
        total_processed = self.successful_submissions + self.failed_submissions
        return {
            "total": total_processed,
            "successful": self.successful_submissions,
            "failed": self.failed_submissions,
            "success_rate": self.successful_submissions / total_processed if total_processed > 0 else 0,
            "cancelled": self.is_cancelled()
        }

    def get_progress(self) -> Dict[str, Any]:
        """
        Get current submission progress

        Returns:
            Dictionary with progress information
        """
        with self._lock:
            return {
                "progress": self.current_progress,
                "total": self.total_submissions,
                "successful": self.successful_submissions,
                "failed": self.failed_submissions,
                "remaining": self.total_submissions - (self.successful_submissions + self.failed_submissions)
            }
