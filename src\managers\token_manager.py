"""
Token Manager Module for Google Form AutoFill

This module provides functionality for creating, validating, and managing
secure tokens for shareable links.
"""

import json
import os
import time
import uuid
import hashlib
import secrets
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta


class TokenManager:
    """Class for managing secure tokens for shareable links"""
    
    def __init__(self, storage_dir: str = "responses/tokens"):
        """
        Initialize the token manager
        
        Args:
            storage_dir: Directory to store token files
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        
    def _get_tokens_filename(self) -> str:
        """
        Get the filename for the tokens database
        
        Returns:
            The filename for the tokens database
        """
        return os.path.join(self.storage_dir, "tokens.json")
        
    def _load_tokens(self) -> Dict[str, Dict[str, Any]]:
        """
        Load the tokens database
        
        Returns:
            Dictionary mapping tokens to their data
        """
        filename = self._get_tokens_filename()
        if not os.path.exists(filename):
            return {}
            
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}
            
    def _save_tokens(self, tokens: Dict[str, Dict[str, Any]]) -> None:
        """
        Save the tokens database
        
        Args:
            tokens: Dictionary mapping tokens to their data
        """
        filename = self._get_tokens_filename()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(tokens, f, ensure_ascii=False, indent=2)
            
    def generate_token(self, resource_type: str, resource_id: str, 
                      expires_in_days: int = 30) -> str:
        """
        Generate a secure token for a resource
        
        Args:
            resource_type: Type of resource (e.g., 'customer_spec')
            resource_id: ID of the resource
            expires_in_days: Number of days until token expires
            
        Returns:
            The generated token
        """
        # Generate a secure random token
        token_bytes = secrets.token_bytes(32)
        token = hashlib.sha256(token_bytes).hexdigest()[:32]
        
        # Calculate expiration time
        expiration = time.time() + (expires_in_days * 24 * 60 * 60)
        
        # Store token data
        tokens = self._load_tokens()
        tokens[token] = {
            "token": token,
            "resource_type": resource_type,
            "resource_id": resource_id,
            "created_at": time.time(),
            "expires_at": expiration,
            "expires_at_readable": datetime.fromtimestamp(expiration).strftime("%Y-%m-%d %H:%M:%S")
        }
        self._save_tokens(tokens)
        
        return token
        
    def validate_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Validate a token and return its associated resource
        
        Args:
            token: The token to validate
            
        Returns:
            Tuple of (is_valid, token_data)
        """
        tokens = self._load_tokens()
        
        if token not in tokens:
            return False, None
            
        token_data = tokens[token]
        
        # Check if token has expired
        if token_data.get("expires_at", 0) < time.time():
            return False, None
            
        return True, token_data
        
    def get_resource_from_token(self, token: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Get the resource associated with a token
        
        Args:
            token: The token to validate
            
        Returns:
            Tuple of (is_valid, resource_type, resource_id)
        """
        is_valid, token_data = self.validate_token(token)
        
        if not is_valid or not token_data:
            return False, None, None
            
        return True, token_data.get("resource_type"), token_data.get("resource_id")
        
    def revoke_token(self, token: str) -> bool:
        """
        Revoke a token
        
        Args:
            token: The token to revoke
            
        Returns:
            True if successful, False otherwise
        """
        tokens = self._load_tokens()
        
        if token not in tokens:
            return False
            
        del tokens[token]
        self._save_tokens(tokens)
        
        return True
        
    def revoke_tokens_for_resource(self, resource_type: str, resource_id: str) -> int:
        """
        Revoke all tokens for a specific resource
        
        Args:
            resource_type: Type of resource
            resource_id: ID of the resource
            
        Returns:
            Number of tokens revoked
        """
        tokens = self._load_tokens()
        tokens_to_remove = []
        
        for token, data in tokens.items():
            if (data.get("resource_type") == resource_type and 
                data.get("resource_id") == resource_id):
                tokens_to_remove.append(token)
                
        for token in tokens_to_remove:
            del tokens[token]
            
        self._save_tokens(tokens)
        
        return len(tokens_to_remove)
        
    def clean_expired_tokens(self) -> int:
        """
        Clean up expired tokens
        
        Returns:
            Number of tokens removed
        """
        tokens = self._load_tokens()
        current_time = time.time()
        tokens_to_remove = []
        
        for token, data in tokens.items():
            if data.get("expires_at", 0) < current_time:
                tokens_to_remove.append(token)
                
        for token in tokens_to_remove:
            del tokens[token]
            
        self._save_tokens(tokens)
        
        return len(tokens_to_remove)
        
    def extend_token_expiration(self, token: str, additional_days: int) -> bool:
        """
        Extend the expiration of a token
        
        Args:
            token: The token to extend
            additional_days: Number of additional days
            
        Returns:
            True if successful, False otherwise
        """
        tokens = self._load_tokens()
        
        if token not in tokens:
            return False
            
        token_data = tokens[token]
        
        # Calculate new expiration time
        current_expiration = token_data.get("expires_at", time.time())
        new_expiration = current_expiration + (additional_days * 24 * 60 * 60)
        
        # Update token data
        token_data["expires_at"] = new_expiration
        token_data["expires_at_readable"] = datetime.fromtimestamp(new_expiration).strftime("%Y-%m-%d %H:%M:%S")
        
        self._save_tokens(tokens)
        
        return True
