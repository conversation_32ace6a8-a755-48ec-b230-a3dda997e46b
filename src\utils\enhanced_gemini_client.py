"""
Enhanced Gemini API Integration for Google Form AutoFill

This module extends the basic Gemini client with advanced capabilities:
- Improved prompt templates for example-based generation
- Style matching for responses
- Quality scoring mechanisms
- Diversity control
- Batch optimization
"""

import json
import re
import time
import random
import logging
from typing import List, Dict, Union, Optional, Any, Tuple

import google.generativeai as genai

from src.utils.gemini_client import GeminiClient, generate_with_retry, parse_open_ended_responses

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EnhancedGeminiClient(GeminiClient):
    """Enhanced client for interacting with Google's Gemini API"""

    def __init__(self, api_key: Union[str, List[str]], model: str = "gemini-2.0-flash-lite", key_strategy: str = "round_robin"):
        """
        Initialize the enhanced Gemini client

        Args:
            api_key: The API key(s) for Gemini (single key or list of keys)
            model: The model to use (default: gemini-2.0-flash-lite)
            key_strategy: Strategy for key selection when multiple keys are provided
                         ("round_robin", "random", or "least_used")
        """
        super().__init__(api_key, model, key_strategy)
        self.system_prompt = "You are an expert at generating diverse, high-quality responses for surveys and forms."

    def generate_text_with_system_prompt(self, prompt: str, system_prompt: Optional[str] = None,
                                        max_tokens: int = 1024, temperature: float = 0.7,
                                        top_p: float = 0.95, top_k: int = 40) -> str:
        """
        Generate text using Gemini API with a system prompt

        Args:
            prompt: The prompt to send to the model
            system_prompt: Optional system prompt to override the default
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0-1.0)
            top_p: Controls diversity via nucleus sampling
            top_k: Controls diversity via vocabulary restriction

        Returns:
            Generated text from the model
        """
        # Use provided system prompt or default
        system = system_prompt if system_prompt is not None else self.system_prompt

        # Configure generation parameters
        generation_config = {
            "max_output_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k
        }

        try:
            # Configure the API with current key
            genai.configure(api_key=self.api_key)

            # Get the model
            model = genai.GenerativeModel(model_name=self.model,
                                       generation_config=generation_config)

            # Generate content with system prompt
            response = model.generate_content([
                {"role": "system", "parts": [system]},
                {"role": "user", "parts": [prompt]}
            ])
            return response.text

        except Exception as e:
            # Handle key rotation if using multiple keys
            if self.multiple_keys:
                logger.warning(f"API key failed: {str(e)}")
                self.key_manager.mark_key_failed(self.api_key)

                # Try with other keys
                while True:
                    try:
                        # Get next key
                        self.api_key = self.key_manager.get_next_key()
                        logger.info(f"Switching to next API key")

                        # Configure the API with new key
                        genai.configure(api_key=self.api_key)

                        # Get the model
                        model = genai.GenerativeModel(model_name=self.model,
                                                   generation_config=generation_config)

                        # Generate content with system prompt
                        response = model.generate_content([
                            {"role": "system", "parts": [system]},
                            {"role": "user", "parts": [prompt]}
                        ])
                        return response.text

                    except Exception as e:
                        # Mark this key as failed too
                        logger.warning(f"API key failed: {str(e)}")
                        self.key_manager.mark_key_failed(self.api_key)

                        # If all keys have been tried and failed, raise exception
                        if len(self.key_manager.failed_keys) >= len(self.key_manager.api_keys):
                            raise Exception("All API keys have failed") from e
            else:
                # Single key mode - just propagate the exception
                raise

    def batch_generate_with_examples(self, questions: List[Dict[str, Any]], 
                                   examples: Dict[str, List[str]],
                                   count_per_question: int = 5,
                                   temperature: float = 0.7,
                                   diversity_level: str = "medium") -> Dict[str, List[Dict[str, Any]]]:
        """
        Generate responses for multiple questions with examples in an optimized batch

        Args:
            questions: List of question objects
            examples: Dictionary mapping question IDs to example responses
            count_per_question: Number of responses to generate per question
            temperature: Controls randomness (0.0-1.0)
            diversity_level: Level of diversity ("low", "medium", "high")

        Returns:
            Dictionary mapping question IDs to lists of response objects with quality scores
        """
        # Adjust temperature based on diversity level
        if diversity_level == "low":
            temperature = max(temperature - 0.2, 0.1)
        elif diversity_level == "high":
            temperature = min(temperature + 0.2, 0.9)

        # Group questions by whether they have examples
        questions_with_examples = []
        questions_without_examples = []
        
        for question in questions:
            question_id = str(question["id"])
            if question_id in examples and examples[question_id]:
                questions_with_examples.append(question)
            else:
                questions_without_examples.append(question)
                
        results = {}
        
        # Process questions with examples
        if questions_with_examples:
            prompt = self._create_batch_examples_prompt(
                questions_with_examples, 
                examples,
                count_per_question
            )
            
            try:
                response_text = self.generate_text_with_system_prompt(
                    prompt,
                    max_tokens=4096,
                    temperature=temperature
                )
                
                parsed_results = self._parse_batch_with_examples(
                    response_text, 
                    questions_with_examples
                )
                
                results.update(parsed_results)
                
            except Exception as e:
                logger.error(f"Error in batch generation with examples: {e}")
                # Fall back to individual processing
                for question in questions_with_examples:
                    question_id = str(question["id"])
                    question_examples = examples.get(question_id, [])
                    
                    try:
                        single_prompt = self._create_example_based_prompt(
                            question["title"],
                            question_examples,
                            count_per_question
                        )
                        
                        single_response = self.generate_text_with_system_prompt(
                            single_prompt,
                            max_tokens=2048,
                            temperature=temperature
                        )
                        
                        parsed = self._parse_responses_with_quality(
                            single_response,
                            count_per_question
                        )
                        
                        results[question_id] = parsed
                        
                    except Exception as e:
                        logger.error(f"Error processing question {question_id}: {e}")
                        results[question_id] = []
        
        # Process questions without examples
        if questions_without_examples:
            prompt = self._create_batch_prompt(
                questions_without_examples,
                count_per_question
            )
            
            try:
                response_text = self.generate_text_with_system_prompt(
                    prompt,
                    max_tokens=4096,
                    temperature=temperature
                )
                
                parsed_results = self._parse_batch_responses(
                    response_text, 
                    questions_without_examples
                )
                
                # Convert to the same format as with examples
                for question_id, responses in parsed_results.items():
                    results[question_id] = [
                        {"response": r, "quality": 0.7} for r in responses
                    ]
                
            except Exception as e:
                logger.error(f"Error in batch generation without examples: {e}")
                # Fall back to individual processing
                for question in questions_without_examples:
                    question_id = str(question["id"])
                    
                    try:
                        single_prompt = create_open_ended_prompt(
                            question["title"],
                            count=count_per_question
                        )
                        
                        single_response = self.generate_text(
                            single_prompt,
                            max_tokens=2048,
                            temperature=temperature
                        )
                        
                        parsed = parse_open_ended_responses(
                            single_response,
                            count_per_question
                        )
                        
                        results[question_id] = [
                            {"response": r, "quality": 0.7} for r in parsed
                        ]
                        
                    except Exception as e:
                        logger.error(f"Error processing question {question_id}: {e}")
                        results[question_id] = []
        
        return results

    def _create_batch_examples_prompt(self, questions: List[Dict[str, Any]], 
                                    examples: Dict[str, List[str]],
                                    count: int) -> str:
        """
        Create a prompt for generating responses for multiple questions with examples

        Args:
            questions: List of question objects
            examples: Dictionary mapping question IDs to example responses
            count: Number of responses to generate per question

        Returns:
            A prompt for the Gemini model
        """
        prompt = f"Generate responses for the following form questions based on the provided examples. For each question, provide {count} unique, thoughtful responses that match the style and content of the examples.\n\n"

        for i, question in enumerate(questions, 1):
            question_id = str(question["id"])
            question_title = question.get("title", "")
            question_examples = examples.get(question_id, [])
            
            prompt += f"Question {i}: {question_title}\n"
            
            if question_examples:
                prompt += "Examples:\n"
                for j, example in enumerate(question_examples, 1):
                    prompt += f"  Example {j}: {example}\n"
            
            prompt += "\n"

        prompt += f"\nFor each question, provide {count} responses that match the style, tone, and content of the examples provided.\n"
        prompt += "For each response, also include a quality score between 0.0 and 1.0 that represents how well the response matches the style of the examples.\n\n"
        
        prompt += "Format your response as a JSON object with question indices as keys and arrays of response objects as values:\n\n"
        prompt += "```json\n"
        prompt += "{\n"
        prompt += '  "1": [\n'
        prompt += '    {"response": "First response text", "quality": 0.95},\n'
        prompt += '    {"response": "Second response text", "quality": 0.87},\n'
        prompt += '    ...\n'
        prompt += '  ],\n'
        prompt += '  "2": [\n'
        prompt += '    {"response": "First response text", "quality": 0.92},\n'
        prompt += '    ...\n'
        prompt += '  ],\n'
        prompt += "  ...\n"
        prompt += "}\n"
        prompt += "```\n\n"
        
        return prompt

    def _create_example_based_prompt(self, question: str, examples: List[str], count: int) -> str:
        """
        Create a prompt for generating responses based on examples

        Args:
            question: The question text
            examples: List of example responses
            count: Number of responses to generate

        Returns:
            A prompt for the Gemini model
        """
        prompt = f"Generate {count} different responses to the following question that match the style and content of the provided examples:\n\n"
        prompt += f"Question: {question}\n\n"
        
        prompt += "Example responses:\n"
        for i, example in enumerate(examples, 1):
            prompt += f"Example {i}: {example}\n"
        
        prompt += f"\nPlease provide {count} unique responses that:\n"
        prompt += "1. Match the tone, style, and level of detail of the examples\n"
        prompt += "2. Are diverse and not repetitive\n"
        prompt += "3. Are realistic and could be written by a real person\n"
        prompt += "4. Maintain similar length and complexity to the examples\n\n"
        
        prompt += "For each response, also provide a quality score between 0.0 and 1.0 that represents how well the response matches the style of the examples.\n\n"
        
        prompt += "Return your response as a JSON array where each element is an object with 'response' and 'quality' fields:\n"
        prompt += "```json\n"
        prompt += '[\n'
        prompt += '  {"response": "First response text", "quality": 0.95},\n'
        prompt += '  {"response": "Second response text", "quality": 0.87},\n'
        prompt += '  ...\n'
        prompt += ']\n'
        prompt += "```\n\n"
        
        return prompt

    def _create_batch_prompt(self, questions: List[Dict[str, Any]], count: int) -> str:
        """
        Create a prompt for generating responses for multiple questions without examples

        Args:
            questions: List of question objects
            count: Number of responses to generate per question

        Returns:
            A prompt for the Gemini model
        """
        prompt = f"Generate responses for the following form questions. For each question, provide {count} unique, thoughtful responses.\n\n"

        for i, question in enumerate(questions, 1):
            question_title = question.get("title", "")
            question_type = question.get("type", "")
            options = question.get("options", [])

            prompt += f"Question {i}: {question_title}\n"
            prompt += f"Type: {question_type}\n"

            if options:
                prompt += "Options:\n"
                for j, option in enumerate(options, 1):
                    prompt += f"  {j}. {option}\n"

            prompt += "\n"

        prompt += f"\nFor each question, provide {count} realistic responses that a real person might give.\n"
        prompt += "Format your response as a JSON object with question indices as keys and an array of responses as values:\n\n"
        prompt += "```json\n"
        prompt += "{\n"
        prompt += '  "1": ["Response 1", "Response 2", ...],\n'
        prompt += '  "2": ["Response 1", "Response 2", ...],\n'
        prompt += "  ...\n"
        prompt += "}\n"
        prompt += "```\n\n"
        prompt += "For multiple-choice questions, responses should only be one of the provided options."

        return prompt

    def _parse_batch_with_examples(self, response_text: str, 
                                 questions: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Parse responses for multiple questions with quality scores from Gemini's output

        Args:
            response_text: The text response from Gemini
            questions: List of question objects

        Returns:
            Dictionary mapping question IDs to lists of response objects with quality scores
        """
        # Extract JSON from the response
        try:
            # Find JSON-like structure between triple backticks or anywhere in the text
            json_pattern = r'```(?:json)?\s*({[\s\S]*?})```|({[\s\S]*})'
            match = re.search(json_pattern, response_text)

            if match:
                json_str = match.group(1) or match.group(2)
                response_dict = json.loads(json_str)

                # Map question indices to question IDs
                result = {}
                for i, question in enumerate(questions, 1):
                    question_id = str(question["id"])
                    if str(i) in response_dict:
                        result[question_id] = response_dict[str(i)]

                return result
        except Exception as e:
            logger.error(f"Error parsing batch responses with examples: {e}")

        # Fallback: try to parse structured text
        result = {}
        current_question = None
        current_responses = []

        for line in response_text.split('\n'):
            line = line.strip()

            # Check for question marker
            question_match = re.match(r'Question\s+(\d+)', line)
            if question_match:
                # Save previous question's responses if any
                if current_question is not None and current_responses:
                    question_id = str(questions[current_question - 1]["id"])
                    result[question_id] = current_responses

                # Start new question
                current_question = int(question_match.group(1))
                current_responses = []
                continue

            # Check for response marker with quality
            response_match = re.match(r'(?:Response|Answer)\s*\d+\s*\(quality:\s*([\d.]+)\):\s*(.*)', line)
            if response_match and current_question is not None:
                quality = float(response_match.group(1))
                response_text = response_match.group(2).strip()
                if response_text:
                    current_responses.append({"response": response_text, "quality": quality})
                continue

            # Check for simple response marker
            simple_response_match = re.match(r'(?:Response|Answer)\s*\d+:\s*(.*)', line)
            if simple_response_match and current_question is not None:
                response_text = simple_response_match.group(1).strip()
                if response_text:
                    current_responses.append({"response": response_text, "quality": 0.7})

        # Save last question's responses
        if current_question is not None and current_responses:
            question_id = str(questions[current_question - 1]["id"])
            result[question_id] = current_responses

        return result

    def _parse_responses_with_quality(self, response_text: str, count: int) -> List[Dict[str, Any]]:
        """
        Parse responses and quality scores from Gemini's output

        Args:
            response_text: The text response from Gemini
            count: Expected number of responses

        Returns:
            List of response objects with quality scores
        """
        responses = []
        
        try:
            # Try to extract JSON array from the response
            json_pattern = r'```(?:json)?\s*(\[[\s\S]*?\])```|(\[[\s\S]*?\])'
            match = re.search(json_pattern, response_text)
            
            if match:
                json_str = match.group(1) or match.group(2)
                response_list = json.loads(json_str)
                
                if isinstance(response_list, list):
                    for item in response_list:
                        if isinstance(item, dict) and 'response' in item and 'quality' in item:
                            response = str(item['response']).strip()
                            quality = float(item['quality'])
                            
                            # Validate quality score
                            quality = max(0.0, min(1.0, quality))
                            
                            if response:
                                responses.append({"response": response, "quality": quality})
            
        except Exception as e:
            logger.error(f"Error parsing JSON responses with quality: {e}")
        
        # Fallback: if parsing failed, try to extract just the responses
        if not responses:
            parsed_responses = parse_open_ended_responses(response_text, count)
            responses = [{"response": r, "quality": 0.7} for r in parsed_responses]
        
        return responses[:count]

    def _parse_batch_responses(self, response_text: str, questions: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        Parse responses for multiple questions from Gemini's output (without quality scores)

        Args:
            response_text: The text response from Gemini
            questions: List of question objects

        Returns:
            Dictionary mapping question IDs to lists of responses
        """
        # Extract JSON from the response
        try:
            # Find JSON-like structure between triple backticks or anywhere in the text
            json_pattern = r'```(?:json)?\s*({[\s\S]*?})```|({[\s\S]*})'
            match = re.search(json_pattern, response_text)

            if match:
                json_str = match.group(1) or match.group(2)
                response_dict = json.loads(json_str)

                # Map question indices to question IDs
                result = {}
                for i, question in enumerate(questions, 1):
                    question_id = str(question["id"])
                    if str(i) in response_dict:
                        result[question_id] = response_dict[str(i)]

                return result
        except Exception as e:
            logger.error(f"Error parsing batch responses: {e}")

        # Fallback: try to parse structured text
        result = {}
        current_question = None
        current_responses = []

        for line in response_text.split('\n'):
            line = line.strip()

            # Check for question marker
            question_match = re.match(r'Question\s+(\d+)', line)
            if question_match:
                # Save previous question's responses if any
                if current_question is not None and current_responses:
                    question_id = str(questions[current_question - 1]["id"])
                    result[question_id] = current_responses

                # Start new question
                current_question = int(question_match.group(1))
                current_responses = []
                continue

            # Check for response marker
            response_match = re.match(r'(?:Response|Answer)\s*\d+:\s*(.*)', line)
            if response_match and current_question is not None:
                response_text = response_match.group(1).strip()
                if response_text:
                    current_responses.append(response_text)

        # Save last question's responses
        if current_question is not None and current_responses:
            question_id = str(questions[current_question - 1]["id"])
            result[question_id] = current_responses

        return result


# Create enhanced versions of the helper functions

def create_open_ended_prompt(question: str, count: int = 1, examples: Optional[List[str]] = None,
                           style_guide: str = "", diversity_level: str = "medium") -> str:
    """
    Create an enhanced prompt for generating responses to open-ended questions

    Args:
        question: The question to generate responses for
        count: Number of responses to generate
        examples: Optional list of example responses
        style_guide: Optional style guidance
        diversity_level: Level of diversity ("low", "medium", "high")

    Returns:
        A prompt for the Gemini model
    """
    # Base prompt
    prompt = f"Generate {count} different realistic responses to the following question:\n\n"
    prompt += f"Question: {question}\n\n"

    # Add examples if provided
    if examples:
        prompt += "Here are some example responses for reference:\n"
        for i, example in enumerate(examples, 1):
            prompt += f"Example {i}: {example}\n"
        prompt += "\n"

    # Add style guidance if provided
    if style_guide:
        prompt += f"Style guidance: {style_guide}\n\n"

    # Add diversity guidance based on level
    if diversity_level == "low":
        prompt += f"Please provide {count} responses that are similar in style and content.\n"
    elif diversity_level == "high":
        prompt += f"Please provide {count} highly diverse responses with different perspectives, lengths, and styles.\n"
    else:  # medium (default)
        prompt += f"Please provide {count} unique, thoughtful responses with a good balance of similarity and diversity.\n"

    prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
    prompt += "Format example:\n"
    prompt += "```json\n"
    prompt += '["Response 1", "Response 2", "Response 3"]\n'
    prompt += "```\n\n"
    prompt += "Make sure each response is complete, meaningful, and appropriate for the question."

    return prompt


def create_style_matched_prompt(question: str, examples: List[str], count: int = 1, 
                              style_guide: str = "") -> str:
    """
    Create a prompt for generating style-matched responses

    Args:
        question: The question text
        examples: List of example responses
        count: Number of responses to generate
        style_guide: Optional style guidance

    Returns:
        A prompt for the Gemini model
    """
    prompt = f"Generate {count} different responses to the following question that match the style and content of the provided examples:\n\n"
    prompt += f"Question: {question}\n\n"
    
    prompt += "Example responses:\n"
    for i, example in enumerate(examples, 1):
        prompt += f"Example {i}: {example}\n"
    
    if style_guide:
        prompt += f"\nStyle guidance: {style_guide}\n"
    
    prompt += f"\nPlease provide {count} unique responses that:\n"
    prompt += "1. Match the tone, style, and level of detail of the examples\n"
    prompt += "2. Are diverse and not repetitive\n"
    prompt += "3. Are realistic and could be written by a real person\n"
    prompt += "4. Maintain similar length and complexity to the examples\n\n"
    
    prompt += "For each response, also provide a quality score between 0.0 and 1.0 that represents how well the response matches the style of the examples.\n\n"
    
    prompt += "Return your response as a JSON array where each element is an object with 'response' and 'quality' fields:\n"
    prompt += "```json\n"
    prompt += '[\n'
    prompt += '  {"response": "First response text", "quality": 0.95},\n'
    prompt += '  {"response": "Second response text", "quality": 0.87},\n'
    prompt += '  ...\n'
    prompt += ']\n'
    prompt += "```\n\n"
    
    return prompt


def parse_style_matched_responses(response_text: str, count: int) -> List[Dict[str, Any]]:
    """
    Parse responses with quality scores from Gemini's output

    Args:
        response_text: The text response from Gemini
        count: Expected number of responses

    Returns:
        List of response objects with quality scores
    """
    responses = []
    
    try:
        # Try to extract JSON array from the response
        json_pattern = r'```(?:json)?\s*(\[[\s\S]*?\])```|(\[[\s\S]*?\])'
        match = re.search(json_pattern, response_text)
        
        if match:
            json_str = match.group(1) or match.group(2)
            response_list = json.loads(json_str)
            
            if isinstance(response_list, list):
                for item in response_list:
                    if isinstance(item, dict) and 'response' in item and 'quality' in item:
                        response = str(item['response']).strip()
                        quality = float(item['quality'])
                        
                        # Validate quality score
                        quality = max(0.0, min(1.0, quality))
                        
                        if response:
                            responses.append({"response": response, "quality": quality})
        
    except Exception as e:
        logger.error(f"Error parsing JSON responses with quality: {e}")
    
    # Fallback: if parsing failed, try to extract just the responses
    if not responses:
        parsed_responses = parse_open_ended_responses(response_text, count)
        responses = [{"response": r, "quality": 0.7} for r in parsed_responses]
    
    return responses[:count]


def generate_with_style_matching(client: GeminiClient, question: str, examples: List[str], 
                               count: int = 5, style_guide: str = "", 
                               max_retries: int = 3) -> List[Dict[str, Any]]:
    """
    Generate style-matched responses with retry logic

    Args:
        client: The GeminiClient instance
        question: The question text
        examples: List of example responses
        count: Number of responses to generate
        style_guide: Optional style guidance
        max_retries: Maximum number of retry attempts

    Returns:
        List of response objects with quality scores
    """
    prompt = create_style_matched_prompt(question, examples, count, style_guide)
    
    retries = 0
    while retries < max_retries:
        try:
            if isinstance(client, EnhancedGeminiClient):
                response_text = client.generate_text_with_system_prompt(prompt, max_tokens=4096)
            else:
                response_text = client.generate_text(prompt, max_tokens=4096)
                
            return parse_style_matched_responses(response_text, count)
            
        except Exception as e:
            retries += 1
            if retries == max_retries:
                logger.error(f"Failed to generate style-matched responses after {max_retries} attempts: {e}")
                return []
                
            sleep_time = 2 ** retries
            logger.warning(f"API error: {e}. Retrying in {sleep_time} seconds...")
            time.sleep(sleep_time)
    
    return []  # Should never reach here, but just in case
