"""
Gemini API Integration for Google Form AutoFill

This module provides integration with Google's Gemini AI model for generating
intelligent responses for form questions.
"""

import json
import re
import time
import random
import logging
from typing import List, Dict, Union, Optional, Any

import google.generativeai as genai

from src.managers.gemini_key_manager import GeminiKeyManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GeminiClient:
    """Client for interacting with Google's Gemini API using the official Python library"""

    def __init__(self, api_key: Union[str, List[str]], model: str = "gemini-2.0-flash-lite", key_strategy: str = "round_robin"):
        """
        Initialize the Gemini client

        Args:
            api_key: The API key(s) for Gemini (single key or list of keys)
            model: The model to use (default: gemini-2.0-flash-lite)
            key_strategy: Strategy for key selection when multiple keys are provided
                         ("round_robin", "random", or "least_used")
        """
        self.model = model

        # Initialize key manager if multiple keys are provided
        if isinstance(api_key, list):
            self.key_manager = GeminiKeyManager(api_key, strategy=key_strategy)
            self.api_key = self.key_manager.get_next_key()  # Initial key
            self.multiple_keys = True
            logger.info(f"Initialized Gemini client with {len(api_key)} API keys using {key_strategy} strategy")
        else:
            self.api_key = api_key
            self.key_manager = None
            self.multiple_keys = False
            logger.info("Initialized Gemini client with a single API key")

        # Configure the Gemini API with initial key
        genai.configure(api_key=self.api_key)

    def generate_text(self, prompt: str, max_tokens: int = 1024,
                      temperature: float = 0.7, top_p: float = 0.95, top_k: int = 40) -> str:
        """
        Generate text using Gemini API with automatic key rotation and fallback

        Args:
            prompt: The prompt to send to the model
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0-1.0)
            top_p: Controls diversity via nucleus sampling
            top_k: Controls diversity via vocabulary restriction

        Returns:
            Generated text from the model

        Raises:
            Exception: If all API keys fail
        """
        # Configure generation parameters
        generation_config = {
            "max_output_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k
        }

        # If using multiple keys, try each key until one works
        if self.multiple_keys:
            # Try with current key first
            try:
                # Configure the API with current key
                genai.configure(api_key=self.api_key)

                # Get the model
                model = genai.GenerativeModel(model_name=self.model,
                                           generation_config=generation_config)

                # Generate content
                response = model.generate_content(prompt)
                return response.text

            except Exception as e:
                # Current key failed, mark it as failed
                logger.warning(f"API key failed: {str(e)}")
                self.key_manager.mark_key_failed(self.api_key)

                # Try with other keys
                while True:
                    try:
                        # Get next key
                        self.api_key = self.key_manager.get_next_key()
                        logger.info(f"Switching to next API key")

                        # Configure the API with new key
                        genai.configure(api_key=self.api_key)

                        # Get the model
                        model = genai.GenerativeModel(model_name=self.model,
                                                   generation_config=generation_config)

                        # Generate content
                        response = model.generate_content(prompt)
                        return response.text

                    except Exception as e:
                        # Mark this key as failed too
                        logger.warning(f"API key failed: {str(e)}")
                        self.key_manager.mark_key_failed(self.api_key)

                        # If all keys have been tried and failed, raise exception
                        if len(self.key_manager.failed_keys) >= len(self.key_manager.api_keys):
                            raise Exception("All API keys have failed") from e
        else:
            # Single key mode - just try once
            # Get the model
            model = genai.GenerativeModel(model_name=self.model,
                                         generation_config=generation_config)

            # Generate content
            response = model.generate_content(prompt)
            return response.text

    def batch_generate(self, prompts: List[str], max_tokens: int = 1024) -> List[str]:
        """
        Generate responses for multiple prompts

        Args:
            prompts: List of prompts to send to the model
            max_tokens: Maximum number of tokens to generate per prompt

        Returns:
            List of generated responses
        """
        results = []
        for prompt in prompts:
            results.append(self.generate_text(prompt, max_tokens))
        return results


def create_open_ended_prompt(question: str, count: int = 1, examples: Optional[List[str]] = None) -> str:
    """
    Create a prompt for generating responses to open-ended questions

    Args:
        question: The question to generate responses for
        count: Number of responses to generate
        examples: Optional list of example responses

    Returns:
        A prompt for the Gemini model
    """
    prompt = f"Generate {count} different realistic responses to the following question:\n\n"
    prompt += f"Question: {question}\n\n"

    if examples:
        prompt += "Here are some example responses for reference:\n"
        for i, example in enumerate(examples, 1):
            prompt += f"Example {i}: {example}\n"
        prompt += "\n"

    prompt += f"Please provide {count} unique, thoughtful responses that a real person might give.\n"
    prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
    prompt += "Format example:\n"
    prompt += "```json\n"
    prompt += '["Response 1", "Response 2", "Response 3"]\n'
    prompt += "```\n\n"
    prompt += "Make sure each response is complete, meaningful, and different from the others."

    return prompt


def create_multiple_choice_prompt(question: str, options: List[str], count: int = 1) -> str:
    """
    Create a prompt for generating weightage for multiple choice options

    Args:
        question: The question to generate responses for
        options: List of available options
        count: Number of responses to distribute

    Returns:
        A prompt for the Gemini model
    """
    prompt = f"For the following multiple choice question, suggest a distribution of {count} responses:\n\n"
    prompt += f"Question: {question}\n"
    prompt += "Options:\n"

    for i, option in enumerate(options, 1):
        prompt += f"{i}. {option}\n"

    prompt += f"\nPlease distribute {count} responses among these options in a realistic way, "
    prompt += "showing how many responses should go to each option. Return the result as a JSON object "
    prompt += "with option text as keys and count as values."

    return prompt


def parse_open_ended_responses(response_text: str, count: int) -> List[str]:
    """
    Parse multiple responses from Gemini's JSON output

    Args:
        response_text: The text response from Gemini
        count: Expected number of responses

    Returns:
        List of parsed responses
    """
    responses = []

    try:
        # First, try to extract JSON array from the response
        # Look for JSON-like structure between triple backticks or anywhere in the text
        json_pattern = r'```(?:json)?\s*(\[[\s\S]*?\])```|(\[[\s\S]*?\])'
        match = re.search(json_pattern, response_text)

        if match:
            json_str = match.group(1) or match.group(2)
            response_list = json.loads(json_str)

            if isinstance(response_list, list):
                responses = [str(item).strip() for item in response_list if str(item).strip()]

        # If JSON parsing failed, try alternative JSON patterns
        if not responses:
            # Try to find JSON array without backticks
            array_pattern = r'\[[\s\S]*?\]'
            matches = re.findall(array_pattern, response_text)

            for match in matches:
                try:
                    response_list = json.loads(match)
                    if isinstance(response_list, list):
                        responses = [str(item).strip() for item in response_list if str(item).strip()]
                        break
                except json.JSONDecodeError:
                    continue

    except Exception as e:
        print(f"Error parsing JSON responses: {e}")

    # Fallback: try to parse old format if JSON parsing failed
    if not responses:
        pattern = r"(?:Response|Answer)\s*(\d+):\s*(.*?)(?=(?:Response|Answer)\s*\d+:|$)"
        matches = re.findall(pattern, response_text, re.DOTALL)

        for _, response in matches:
            responses.append(response.strip())

        # If pattern matching failed, try simple splitting
        if not responses:
            responses = [r.strip() for r in response_text.split("\n\n") if r.strip()]

    # Clean up responses and ensure we have the requested number
    cleaned_responses = []
    for response in responses:
        # Remove any unwanted characters or formatting
        cleaned = response.strip().strip('"').strip("'")
        if cleaned and len(cleaned) > 0:
            cleaned_responses.append(cleaned)

    # Ensure we have at least some responses
    if not cleaned_responses and response_text.strip():
        # Use the entire response as a single answer if parsing completely failed
        cleaned_responses = [response_text.strip()]

    # Return up to the requested count
    return cleaned_responses[:count] if cleaned_responses else []


def parse_multiple_choice_responses(response_text: str) -> Dict[str, int]:
    """
    Parse multiple choice response distribution from Gemini's output

    Args:
        response_text: The text response from Gemini

    Returns:
        Dictionary mapping options to counts
    """
    # Try to extract JSON from the response
    try:
        # Find JSON-like structure in the text
        json_pattern = r'\{[^}]*\}'
        json_match = re.search(json_pattern, response_text)
        if json_match:
            return json.loads(json_match.group(0))

        # If no JSON found, try to parse structured text
        result = {}
        lines = response_text.strip().split('\n')
        for line in lines:
            if ':' in line:
                option, count_str = line.split(':', 1)
                count = int(re.search(r'\d+', count_str).group(0))
                result[option.strip()] = count
        return result
    except Exception:
        # If parsing fails, return empty dict
        return {}


def generate_with_retry(client: GeminiClient, prompt: str, max_retries: int = 3,
                        backoff_factor: int = 2, max_tokens: int = 1024) -> str:
    """
    Generate text with retry logic

    Args:
        client: The GeminiClient instance
        prompt: The prompt to send
        max_retries: Maximum number of retry attempts
        backoff_factor: Factor to increase wait time between retries
        max_tokens: Maximum number of tokens to generate

    Returns:
        Generated text from the model

    Raises:
        Exception: If all retries fail
    """
    retries = 0
    while retries < max_retries:
        try:
            return client.generate_text(prompt, max_tokens=max_tokens)
        except Exception as e:
            retries += 1
            if retries == max_retries:
                raise
            sleep_time = backoff_factor ** retries
            logger.warning(f"API error: {e}. Retrying in {sleep_time} seconds...")
            time.sleep(sleep_time)


def assign_weightage(responses: List[str], total_submissions: int = 100) -> Dict[str, int]:
    """
    Assign random weightage to responses that sum to total_submissions

    Args:
        responses: List of responses to assign weightage to
        total_submissions: Total number of submissions to distribute

    Returns:
        Dictionary mapping responses to their weightage
    """
    if not responses:
        return {}

    # Start with equal distribution
    base_weight = total_submissions // len(responses)
    weights = [base_weight] * len(responses)

    # Distribute remaining submissions randomly
    remaining = total_submissions - sum(weights)
    for i in range(remaining):
        weights[random.randint(0, len(responses) - 1)] += 1

    return {response: weight for response, weight in zip(responses, weights)}


def create_batch_prompt(questions: List[Dict[str, Any]], sample_count: int = 5) -> str:
    """
    Create a prompt for generating responses for multiple questions in a single request

    Args:
        questions: List of question objects
        sample_count: Number of responses to generate per question

    Returns:
        A prompt for the Gemini model
    """
    prompt = f"Generate responses for the following form questions. For each question, provide {sample_count} unique, thoughtful responses.\n\n"

    for i, question in enumerate(questions, 1):
        question_title = question.get("title", "")
        question_type = question.get("type", "")
        options = question.get("options", [])

        prompt += f"Question {i}: {question_title}\n"
        prompt += f"Type: {question_type}\n"

        if options:
            prompt += "Options:\n"
            for j, option in enumerate(options, 1):
                prompt += f"  {j}. {option}\n"

        prompt += "\n"

    prompt += f"\nFor each question, provide {sample_count} realistic responses that a real person might give.\n"
    prompt += "Format your response as a JSON object with question indices as keys and an array of responses as values:\n\n"
    prompt += "```json\n"
    prompt += "{\n"
    prompt += '  "1": ["Response 1", "Response 2", ...],\n'
    prompt += '  "2": ["Response 1", "Response 2", ...],\n'
    prompt += "  ...\n"
    prompt += "}\n"
    prompt += "```\n\n"
    prompt += "For multiple-choice questions, responses should only be one of the provided options."

    return prompt


def parse_batch_responses(response_text: str, questions: List[Dict[str, Any]]) -> Dict[str, List[str]]:
    """
    Parse responses for multiple questions from Gemini's output

    Args:
        response_text: The text response from Gemini
        questions: List of question objects

    Returns:
        Dictionary mapping question IDs to lists of responses
    """
    # Extract JSON from the response
    try:
        # Find JSON-like structure between triple backticks or anywhere in the text
        json_pattern = r'```(?:json)?\s*({[\s\S]*?})```|({[\s\S]*})'
        match = re.search(json_pattern, response_text)

        if match:
            json_str = match.group(1) or match.group(2)
            response_dict = json.loads(json_str)

            # Map question indices to question IDs
            result = {}
            for i, question in enumerate(questions, 1):
                question_id = question["id"]
                if str(i) in response_dict:
                    result[question_id] = response_dict[str(i)]

            return result
    except Exception as e:
        print(f"Error parsing batch responses: {e}")

    # Fallback: try to parse structured text
    result = {}
    current_question = None
    current_responses = []

    for line in response_text.split('\n'):
        line = line.strip()

        # Check for question marker
        question_match = re.match(r'Question\s+(\d+)', line)
        if question_match:
            # Save previous question's responses if any
            if current_question is not None and current_responses:
                question_id = questions[current_question - 1]["id"]
                result[question_id] = current_responses

            # Start new question
            current_question = int(question_match.group(1))
            current_responses = []
            continue

        # Check for response marker
        response_match = re.match(r'(?:Response|Answer)\s*\d+:\s*(.*)', line)
        if response_match and current_question is not None:
            response_text = response_match.group(1).strip()
            if response_text:
                current_responses.append(response_text)

    # Save last question's responses
    if current_question is not None and current_responses:
        question_id = questions[current_question - 1]["id"]
        result[question_id] = current_responses

    return result
