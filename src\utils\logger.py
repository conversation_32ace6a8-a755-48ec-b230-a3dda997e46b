"""
Logger Module for Google Form AutoFill

This module provides logging functionality for the application.
"""

import os
import logging
import time
from typing import Optional

from src.managers.config_manager import ConfigManager

class Logger:
    """Class for application logging"""
    
    # Log levels
    LEVELS = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    
    def __init__(self, name: str = "googleform-autofill", log_dir: str = "logs"):
        """
        Initialize the logger
        
        Args:
            name: Logger name
            log_dir: Directory to store log files
        """
        self.name = name
        self.log_dir = log_dir
        self.config_manager = ConfigManager()
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)  # Set to lowest level, handlers will filter
        
        # Clear existing handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
            
        # Create log directory if it doesn't exist
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        # Configure logger based on settings
        self._configure_logger()
        
    def _configure_logger(self):
        """Configure logger based on settings"""
        # Get logging configuration
        log_config = self.config_manager.get_value("logging", {})
        log_level = log_config.get("level", "INFO")
        file_enabled = log_config.get("file_enabled", True)
        console_enabled = log_config.get("console_enabled", True)
        
        # Set log level
        level = self.LEVELS.get(log_level, logging.INFO)
        
        # Create file handler if enabled
        if file_enabled:
            log_file = os.path.join(self.log_dir, f"{self.name}_{time.strftime('%Y%m%d')}.log")
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(level)
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
            
        # Create console handler if enabled
        if console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(level)
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
            
    def update_config(self):
        """Update logger configuration based on current settings"""
        # Clear existing handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
            
        # Reconfigure logger
        self._configure_logger()
        
    def debug(self, message: str):
        """Log a debug message"""
        self.logger.debug(message)
        
    def info(self, message: str):
        """Log an info message"""
        self.logger.info(message)
        
    def warning(self, message: str):
        """Log a warning message"""
        self.logger.warning(message)
        
    def error(self, message: str):
        """Log an error message"""
        self.logger.error(message)
        
    def critical(self, message: str):
        """Log a critical message"""
        self.logger.critical(message)
        
    def exception(self, message: str):
        """Log an exception message with traceback"""
        self.logger.exception(message)
        
# Create a global logger instance
app_logger = Logger()

def get_logger(name: Optional[str] = None) -> Logger:
    """
    Get a logger instance
    
    Args:
        name: Logger name (uses global logger if None)
        
    Returns:
        Logger instance
    """
    if name is None:
        return app_logger
        
    return Logger(name)
