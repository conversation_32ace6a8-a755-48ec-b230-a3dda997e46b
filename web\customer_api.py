"""
Customer API Module for Google Form AutoFill Web Interface

This module defines the API endpoints for customer response specifications.
"""

import json
import os
from flask import jsonify, request, url_for, current_app
from typing import Dict, List, Any, Optional

from src.utils.customer_specification import CustomerSpecification
from src.managers.token_manager import TokenManager
from src.core.response_storage import FormResponseManager


def register_customer_api_routes(app):
    """Register customer API routes with the Flask application"""
    
    # Initialize managers
    customer_spec_manager = CustomerSpecification()
    token_manager = TokenManager()
    form_manager = FormResponseManager()
    
    @app.route('/api/customer/specifications', methods=['GET'])
    def api_list_specifications():
        """List all customer specifications"""
        specifications = customer_spec_manager.list_specifications()
        return jsonify(specifications)
    
    @app.route('/api/customer/specifications', methods=['POST'])
    def api_create_specification():
        """Create a new customer specification"""
        data = request.json
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        form_id = data.get('form_id')
        name = data.get('name')
        customer_name = data.get('customer_name')
        description = data.get('description', '')
        
        if not all([form_id, name, customer_name]):
            return jsonify({'error': 'Missing required fields: form_id, name, customer_name'}), 400
            
        # Create the specification
        spec_id = customer_spec_manager.create_specification(
            form_id=form_id,
            name=name,
            customer_name=customer_name,
            description=description
        )
        
        if not spec_id:
            return jsonify({'error': 'Failed to create specification'}), 500
            
        # Load the created specification
        spec_data = customer_spec_manager.load_specification(spec_id)
        
        return jsonify({
            'success': True,
            'message': 'Specification created successfully',
            'specification': spec_data
        }), 201
    
    @app.route('/api/customer/specifications/<spec_id>', methods=['GET'])
    def api_get_specification(spec_id):
        """Get a specific customer specification"""
        spec_data = customer_spec_manager.load_specification(spec_id)
        
        if not spec_data:
            return jsonify({'error': 'Specification not found'}), 404
            
        return jsonify(spec_data)
    
    @app.route('/api/customer/specifications/<spec_id>', methods=['PUT'])
    def api_update_specification(spec_id):
        """Update a customer specification"""
        data = request.json
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        # Load existing specification
        spec_data = customer_spec_manager.load_specification(spec_id)
        if not spec_data:
            return jsonify({'error': 'Specification not found'}), 404
            
        # Update fields
        for key in ['name', 'customer_name', 'description']:
            if key in data:
                spec_data[key] = data[key]
                
        # Save the updated specification
        customer_spec_manager.save_specification(spec_data)
        
        return jsonify({
            'success': True,
            'message': 'Specification updated successfully',
            'specification': spec_data
        })
    
    @app.route('/api/customer/specifications/<spec_id>', methods=['DELETE'])
    def api_delete_specification(spec_id):
        """Delete a customer specification"""
        # Revoke any tokens associated with this specification
        token_manager.revoke_tokens_for_resource('customer_spec', spec_id)
        
        # Delete the specification
        success = customer_spec_manager.delete_specification(spec_id)
        
        if not success:
            return jsonify({'error': 'Failed to delete specification or specification not found'}), 404
            
        return jsonify({
            'success': True,
            'message': 'Specification deleted successfully'
        })
    
    @app.route('/api/customer/forms/<form_id>/questions', methods=['GET'])
    def api_get_form_questions(form_id):
        """Get form questions for customer configuration"""
        form_data = form_manager.load_form_data(form_id)
        
        if not form_data:
            return jsonify({'error': 'Form not found'}), 404
            
        # Extract relevant question data
        questions = []
        for question in form_data.get('questions', []):
            question_type = question.get('type', '')
            question_type_id = question.get('type_id', -1)
            
            # Categorize questions
            category = 'other'
            if question_type in ['short_answer', 'paragraph']:
                category = 'open_ended'
            elif question_type in ['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating']:
                category = 'option_based'
                
            questions.append({
                'id': question.get('id'),
                'title': question.get('title', ''),
                'type': question_type,
                'type_id': question_type_id,
                'category': category,
                'options': question.get('options', []),
                'required': question.get('required', False)
            })
            
        return jsonify({
            'form_id': form_id,
            'form_title': form_data.get('form_title', ''),
            'questions': questions
        })
    
    @app.route('/api/customer/specifications/<spec_id>/weights', methods=['POST'])
    def api_set_question_weights(spec_id):
        """Set weights for option-based questions"""
        data = request.json
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        question_id = data.get('question_id')
        weights = data.get('weights')
        
        if not question_id or not weights:
            return jsonify({'error': 'Missing required fields: question_id, weights'}), 400
            
        # Validate weights
        if not isinstance(weights, dict):
            return jsonify({'error': 'Weights must be a dictionary mapping options to weights'}), 400
            
        # Set the weights
        success = customer_spec_manager.set_question_weights(spec_id, question_id, weights)
        
        if not success:
            return jsonify({'error': 'Failed to set weights or specification not found'}), 404
            
        return jsonify({
            'success': True,
            'message': 'Weights set successfully'
        })
    
    @app.route('/api/customer/specifications/<spec_id>/weights/<question_id>', methods=['GET'])
    def api_get_question_weights(spec_id, question_id):
        """Get weights for option-based questions"""
        weights = customer_spec_manager.get_question_weights(spec_id, question_id)
        
        return jsonify({
            'question_id': question_id,
            'weights': weights
        })
    
    @app.route('/api/customer/specifications/<spec_id>/examples', methods=['POST'])
    def api_set_question_examples(spec_id):
        """Set example responses for open-ended questions"""
        data = request.json
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        question_id = data.get('question_id')
        examples = data.get('examples')
        
        if not question_id or not examples:
            return jsonify({'error': 'Missing required fields: question_id, examples'}), 400
            
        # Validate examples
        if not isinstance(examples, list):
            return jsonify({'error': 'Examples must be a list of strings'}), 400
            
        # Set the examples
        success = customer_spec_manager.set_question_examples(spec_id, question_id, examples)
        
        if not success:
            return jsonify({'error': 'Failed to set examples or specification not found'}), 404
            
        return jsonify({
            'success': True,
            'message': 'Examples set successfully'
        })
    
    @app.route('/api/customer/specifications/<spec_id>/examples/<question_id>', methods=['GET'])
    def api_get_question_examples(spec_id, question_id):
        """Get example responses for open-ended questions"""
        examples = customer_spec_manager.get_question_examples(spec_id, question_id)
        
        return jsonify({
            'question_id': question_id,
            'examples': examples
        })
    
    @app.route('/api/customer/specifications/<spec_id>/share', methods=['POST'])
    def api_generate_share_link(spec_id):
        """Generate a shareable link for a specification"""
        data = request.json or {}
        
        # Check if specification exists
        spec_data = customer_spec_manager.load_specification(spec_id)
        if not spec_data:
            return jsonify({'error': 'Specification not found'}), 404
            
        # Get expiration days
        expires_in_days = data.get('expires_in_days', 30)
        try:
            expires_in_days = int(expires_in_days)
            if expires_in_days < 1:
                expires_in_days = 30
        except (ValueError, TypeError):
            expires_in_days = 30
            
        # Generate token
        token = token_manager.generate_token('customer_spec', spec_id, expires_in_days)
        
        # Update specification with token
        spec_data['share_token'] = token
        customer_spec_manager.save_specification(spec_data)
        
        # Generate shareable URL
        share_url = url_for('api_access_shared_specification', token=token, _external=True)
        
        return jsonify({
            'success': True,
            'message': 'Share link generated successfully',
            'token': token,
            'share_url': share_url,
            'expires_in_days': expires_in_days
        })
    
    @app.route('/api/customer/share/<token>', methods=['GET'])
    def api_access_shared_specification(token):
        """Access a shared specification"""
        # Validate token
        is_valid, resource_type, resource_id = token_manager.get_resource_from_token(token)
        
        if not is_valid or resource_type != 'customer_spec':
            return jsonify({'error': 'Invalid or expired token'}), 403
            
        # Load specification
        spec_data = customer_spec_manager.load_specification(resource_id)
        if not spec_data:
            return jsonify({'error': 'Specification not found'}), 404
            
        return jsonify({
            'success': True,
            'specification': spec_data
        })
    
    @app.route('/api/customer/specifications/<spec_id>/apply', methods=['POST'])
    def api_apply_specification(spec_id):
        """Apply a customer specification to a form"""
        data = request.json or {}
        
        # Get total responses
        total_responses = data.get('total_responses', 100)
        try:
            total_responses = int(total_responses)
            if total_responses < 1:
                total_responses = 100
        except (ValueError, TypeError):
            total_responses = 100
            
        # Apply specification
        success = customer_spec_manager.apply_specification_to_form(spec_id, total_responses)
        
        if not success:
            return jsonify({'error': 'Failed to apply specification or specification not found'}), 404
            
        return jsonify({
            'success': True,
            'message': f'Specification applied successfully with {total_responses} total responses'
        })
    
    # Token management endpoints
    @app.route('/api/customer/tokens/revoke/<token>', methods=['POST'])
    def api_revoke_token(token):
        """Revoke a token"""
        success = token_manager.revoke_token(token)
        
        if not success:
            return jsonify({'error': 'Failed to revoke token or token not found'}), 404
            
        return jsonify({
            'success': True,
            'message': 'Token revoked successfully'
        })
    
    @app.route('/api/customer/tokens/extend/<token>', methods=['POST'])
    def api_extend_token(token):
        """Extend a token's expiration"""
        data = request.json or {}
        
        # Get additional days
        additional_days = data.get('additional_days', 30)
        try:
            additional_days = int(additional_days)
            if additional_days < 1:
                additional_days = 30
        except (ValueError, TypeError):
            additional_days = 30
            
        # Extend token
        success = token_manager.extend_token_expiration(token, additional_days)
        
        if not success:
            return jsonify({'error': 'Failed to extend token or token not found'}), 404
            
        return jsonify({
            'success': True,
            'message': f'Token expiration extended by {additional_days} days'
        })
    
    @app.route('/api/customer/tokens/clean', methods=['POST'])
    def api_clean_expired_tokens():
        """Clean expired tokens"""
        removed_count = token_manager.clean_expired_tokens()
        
        return jsonify({
            'success': True,
            'message': f'Cleaned {removed_count} expired tokens'
        })
