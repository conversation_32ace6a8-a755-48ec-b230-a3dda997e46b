{% extends "base.html" %}

{% block title %}Customer Form Configuration - Google Form AutoFill{% endblock %}

{% block head %}
<style>
    .question-card {
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        background: var(--bg-card) !important;
        border: 1px solid var(--border-color) !important;
    }

    .question-card:hover {
        box-shadow: var(--shadow-lg);
        border-color: var(--border-light);
    }

    .question-card .card-body {
        background: var(--bg-card) !important;
        color: var(--text-primary) !important;
    }

    .question-card .card-header {
        background: var(--bg-tertiary) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color);
    }

    .slider-container {
        padding: 1rem 0;
    }

    .slider-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .percentage-display {
        font-weight: 600;
        min-width: 3rem;
        text-align: right;
        color: var(--text-primary);
        background: var(--accent-primary);
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-size: 0.875rem;
    }

    .option-row {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .option-label {
        flex: 1;
        margin-right: 1rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .option-slider {
        flex: 2;
    }

    .form-range {
        background: var(--bg-tertiary);
    }

    .form-range::-webkit-slider-thumb {
        background: var(--accent-primary);
    }

    .form-range::-moz-range-thumb {
        background: var(--accent-primary);
        border: none;
    }

    .example-textarea {
        margin-bottom: 1rem;
    }

    .total-percentage {
        font-weight: 600;
        font-size: 1.1rem;
        margin-top: 1rem;
        padding: 0.75rem;
        border-radius: var(--radius-sm);
        text-align: center;
        border: 2px solid;
    }

    .percentage-valid {
        background: rgba(0, 184, 148, 0.1);
        color: var(--accent-success);
        border-color: var(--accent-success);
    }

    .percentage-invalid {
        background: rgba(225, 112, 85, 0.1);
        color: var(--accent-danger);
        border-color: var(--accent-danger);
    }

    .form-section-title {
        margin: 2rem 0 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .text-response-config {
        color: var(--text-primary);
    }

    .text-response-config h6 {
        color: var(--text-primary) !important;
        margin-bottom: 0.75rem;
    }

    .text-response-config p {
        color: var(--text-secondary);
    }

    /* Advanced question type styles */
    .combination-item {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-color) !important;
        border-radius: var(--radius-sm);
        transition: all 0.3s ease;
    }

    .combination-item:hover {
        border-color: var(--border-light) !important;
        box-shadow: var(--shadow-sm);
    }

    .range-item {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-color) !important;
        border-radius: var(--radius-sm);
        transition: all 0.3s ease;
    }

    .range-item:hover {
        border-color: var(--border-light) !important;
        box-shadow: var(--shadow-sm);
    }

    .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .form-check-inline {
        margin-right: 1rem;
    }

    .add-multi-grid-combination,
    .add-checkbox-grid-combination,
    .add-checkbox-combination,
    .add-date-range,
    .add-time-range {
        margin-top: 1rem;
    }

    .remove-combination,
    .remove-range {
        margin-top: 0.5rem;
    }

    .grid-config .table {
        background: var(--bg-card);
        color: var(--text-primary);
    }

    .grid-config .table th,
    .grid-config .table td {
        border-color: var(--border-color);
        color: var(--text-primary);
    }

    .grid-config .table thead th {
        background: var(--bg-tertiary);
        border-bottom: 2px solid var(--border-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-cog me-2"></i>Customer Form Configuration
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5>{{ form_data.form_title }}</h5>
                        {% if form_data.form_description %}
                        <p>{{ form_data.form_description }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Form Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Instructions:</strong> Configure how you want the responses to be generated for this form. For multiple-choice questions, set the percentage distribution for each option. For text questions, provide example responses.
        </div>

        <form method="POST" id="customerConfigForm" class="needs-validation" novalidate>
            {{ form.csrf_token }}

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>General Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="total_responses" class="form-label">{{ form.total_responses.label }}</label>
                                {{ form.total_responses(class="form-control", id="total_responses", min=1, max=10000) }}
                                <div class="form-text">Specify how many total form submissions you need.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="form-section-title">Question Configuration</h4>

            {% for question in form_data.questions %}
            <div class="card question-card" id="question-{{ question.id }}">
                <div class="card-header">
                    <h5 class="mb-0">
                        <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %} me-2">
                            {% if question.required %}Required{% else %}Optional{% endif %}
                        </span>
                        {{ question.title }}
                        <span class="badge bg-info ms-2">{{ question.type_name }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if question.description %}
                    <p class="text-muted mb-3">{{ question.description }}</p>
                    {% endif %}

                    {% if question.type in ['multiple_choice', 'dropdown', 'linear_scale', 'rating'] %}
                        <!-- Option-based questions (multiple choice, dropdown, linear scale, rating) -->
                        <div class="option-distribution">
                            <h6>Response Distribution</h6>
                            <p class="text-muted">Set the percentage of responses for each option. The total must equal 100%.</p>

                            <div class="option-sliders" data-question-id="{{ question.id }}">
                                {% for option in question.options %}
                                {% set question_id = question.id|string %}
                                {% set question_config = form_data.customer_config.questions.get(question_id, {}) if form_data.customer_config else {} %}
                                {% set option_percentage = question_config.options.get(option, 100 // question.options|length) if question_config.get('options') else 100 // question.options|length %}
                                <div class="option-row">
                                    <div class="option-label">{{ option }}</div>
                                    <div class="option-slider">
                                        <input type="range" class="form-range percentage-slider"
                                               name="question_{{ question.id }}_option_{{ option|replace(' ', '_') }}"
                                               min="0" max="100" value="{{ option_percentage }}"
                                               data-question-id="{{ question.id }}">
                                    </div>
                                    <div class="percentage-display">{{ option_percentage }}%</div>
                                </div>
                                {% endfor %}
                            </div>

                            <div class="total-percentage percentage-valid" id="total-percentage-{{ question.id }}">
                                Total: 100%
                            </div>
                        </div>
                    {% elif question.type == 'multiple_choice_grid' %}
                        <!-- Multiple Choice Grid questions -->
                        <div class="grid-config">
                            <h6>Multiple Choice Grid Configuration</h6>
                            <p class="text-muted">Configure combinations of row-column selections with different weights. Each row can have only one selection.</p>

                            <!-- Display available grid options -->
                            {% if question.grid_rows and question.grid_columns %}
                            <div class="mb-3">
                                <h6>Available Grid Options:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Row</th>
                                                {% for column in question.grid_columns %}
                                                <th class="text-center">{{ column }}</th>
                                                {% endfor %}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for row in question.grid_rows %}
                                            <tr>
                                                <td><strong>{{ row }}</strong></td>
                                                {% for column in question.grid_columns %}
                                                <td class="text-center">
                                                    <span class="badge bg-light text-dark border">{{ row }}: {{ column }}</span>
                                                </td>
                                                {% endfor %}
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Combination Configuration -->
                            <div id="multiGridCombinations_{{ loop.index0 }}" class="multi-grid-combinations">
                                <div class="combination-item mb-3 p-3 border rounded">
                                    <h6>Combination 1</h6>
                                    {% for row in question.grid_rows %}
                                    <div class="mb-2">
                                        <label class="form-label">{{ row }}:</label>
                                        <select class="form-select" name="multi_grid_{{ loop.index0 }}_row_{{ loop.index0 }}">
                                            <option value="">-- Select Option --</option>
                                            {% for column in question.grid_columns %}
                                            <option value="{{ column }}">{{ column }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    {% endfor %}
                                    <div class="mb-2">
                                        <label class="form-label">Weight:</label>
                                        <input type="number" class="form-control" name="multi_grid_{{ loop.index0 }}_weight" value="1" min="1">
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-combination">Remove</button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm add-multi-grid-combination" data-question="{{ loop.index0 }}">
                                <i class="fas fa-plus me-1"></i>Add Another Combination
                            </button>

                            <!-- Hidden field to store JSON data -->
                            <input type="hidden" name="question_{{ question.id }}_multi_grid_data" id="multiGridData_{{ loop.index0 }}" value="">
                            {% endif %}
                        </div>
                    {% elif question.type == 'checkbox_grid' %}
                        <!-- Checkbox Grid questions -->
                        <div class="grid-config">
                            <h6>Checkbox Grid Configuration</h6>
                            <p class="text-muted">Configure combinations of row-column selections with different weights. Multiple selections per row are allowed.</p>

                            <!-- Display available grid options -->
                            {% if question.grid_rows and question.grid_columns %}
                            <div class="mb-3">
                                <h6>Available Grid Options:</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>Row</th>
                                                {% for column in question.grid_columns %}
                                                <th class="text-center">{{ column }}</th>
                                                {% endfor %}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for row in question.grid_rows %}
                                            <tr>
                                                <td><strong>{{ row }}</strong></td>
                                                {% for column in question.grid_columns %}
                                                <td class="text-center">
                                                    <span class="badge bg-light text-dark border">{{ row }}: {{ column }}</span>
                                                </td>
                                                {% endfor %}
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Combination Configuration -->
                            <div id="checkboxGridCombinations_{{ loop.index0 }}" class="checkbox-grid-combinations">
                                <div class="combination-item mb-3 p-3 border rounded">
                                    <h6>Combination 1</h6>
                                    {% for row in question.grid_rows %}
                                    <div class="mb-2">
                                        <label class="form-label">{{ row }}:</label>
                                        <div class="checkbox-group">
                                            {% for column in question.grid_columns %}
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox"
                                                       name="checkbox_grid_{{ loop.index0 }}_row_{{ loop.index0 }}[]"
                                                       value="{{ column }}" id="cb_{{ loop.index0 }}_{{ loop.index0 }}_{{ loop.index0 }}">
                                                <label class="form-check-label" for="cb_{{ loop.index0 }}_{{ loop.index0 }}_{{ loop.index0 }}">
                                                    {{ column }}
                                                </label>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                    <div class="mb-2">
                                        <label class="form-label">Weight:</label>
                                        <input type="number" class="form-control" name="checkbox_grid_{{ loop.index0 }}_weight" value="1" min="1">
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-combination">Remove</button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm add-checkbox-grid-combination" data-question="{{ loop.index0 }}">
                                <i class="fas fa-plus me-1"></i>Add Another Combination
                            </button>

                            <!-- Hidden field to store JSON data -->
                            <input type="hidden" name="question_{{ question.id }}_checkbox_grid_data" id="checkboxGridData_{{ loop.index0 }}" value="">
                            {% endif %}
                        </div>
                    {% elif question.type == 'checkboxes' %}
                        <!-- Checkbox questions -->
                        <div class="checkbox-config">
                            <h6>Checkbox Configuration</h6>
                            <p class="text-muted">Configure combinations of multiple selections with different weights.</p>

                            <!-- Display available options -->
                            <div class="mb-3">
                                <h6>Available Options:</h6>
                                <div class="row">
                                    {% for option in question.options %}
                                    <div class="col-md-6 mb-2">
                                        <span class="badge bg-light text-dark border">{{ option }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <!-- Combination Configuration -->
                            <div id="checkboxCombinations_{{ loop.index0 }}" class="checkbox-combinations">
                                <div class="combination-item mb-3 p-3 border rounded">
                                    <h6>Combination 1</h6>
                                    <div class="mb-2">
                                        <label class="form-label">Select Options:</label>
                                        <div class="checkbox-group">
                                            {% for option in question.options %}
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="checkbox_{{ loop.index0 }}_options[]"
                                                       value="{{ option }}" id="cb_opt_{{ loop.index0 }}_{{ loop.index0 }}">
                                                <label class="form-check-label" for="cb_opt_{{ loop.index0 }}_{{ loop.index0 }}">
                                                    {{ option }}
                                                </label>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">Weight:</label>
                                        <input type="number" class="form-control" name="checkbox_{{ loop.index0 }}_weight" value="1" min="1">
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-combination">Remove</button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm add-checkbox-combination" data-question="{{ loop.index0 }}">
                                <i class="fas fa-plus me-1"></i>Add Another Combination
                            </button>

                            <!-- Hidden field to store JSON data -->
                            <input type="hidden" name="question_{{ question.id }}_checkbox_data" id="checkboxData_{{ loop.index0 }}" value="">
                        </div>
                    {% elif question.type == 'date' %}
                        <!-- Date questions -->
                        <div class="date-config">
                            <h6>Date Configuration</h6>
                            <p class="text-muted">Configure date ranges for random date generation.</p>

                            <!-- Date Range Configuration -->
                            <div id="dateRanges_{{ loop.index0 }}" class="date-ranges">
                                <div class="range-item mb-3 p-3 border rounded">
                                    <h6>Date Range 1</h6>
                                    <div class="row">
                                        <div class="col-md-5">
                                            <label class="form-label">Start Date:{% if question.required %} <span class="text-danger">*</span>{% endif %}</label>
                                            <input type="date" class="form-control" name="date_{{ loop.index0 }}_start"{% if question.required %} required{% endif %}>
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">End Date:{% if question.required %} <span class="text-danger">*</span>{% endif %}</label>
                                            <input type="date" class="form-control" name="date_{{ loop.index0 }}_end"{% if question.required %} required{% endif %}>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Weight:</label>
                                            <input type="number" class="form-control" name="date_{{ loop.index0 }}_weight" value="1" min="1" required>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-sm btn-danger remove-range">
                                            <i class="fas fa-trash me-1"></i>Remove Range
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm add-date-range" data-question="{{ loop.index0 }}">
                                <i class="fas fa-plus me-1"></i>Add Another Date Range
                            </button>

                            <!-- Hidden field to store JSON data -->
                            <input type="hidden" name="question_{{ question.id }}_date_ranges" id="dateRanges_{{ loop.index0 }}" value="">
                        </div>
                    {% elif question.type == 'time' %}
                        <!-- Time questions -->
                        <div class="time-config">
                            <h6>Time Configuration</h6>
                            <p class="text-muted">Configure time ranges for random time generation.</p>

                            <!-- Time Range Configuration -->
                            <div id="timeRanges_{{ loop.index0 }}" class="time-ranges">
                                <div class="range-item mb-3 p-3 border rounded">
                                    <h6>Time Range 1</h6>
                                    <div class="row">
                                        <div class="col-md-5">
                                            <label class="form-label">Start Time:{% if question.required %} <span class="text-danger">*</span>{% endif %}</label>
                                            <input type="time" class="form-control" name="time_{{ loop.index0 }}_start"{% if question.required %} required{% endif %}>
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">End Time:{% if question.required %} <span class="text-danger">*</span>{% endif %}</label>
                                            <input type="time" class="form-control" name="time_{{ loop.index0 }}_end"{% if question.required %} required{% endif %}>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Weight:</label>
                                            <input type="number" class="form-control" name="time_{{ loop.index0 }}_weight" value="1" min="1" required>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-sm btn-danger remove-range">
                                            <i class="fas fa-trash me-1"></i>Remove Range
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm add-time-range" data-question="{{ loop.index0 }}">
                                <i class="fas fa-plus me-1"></i>Add Another Time Range
                            </button>

                            <!-- Hidden field to store JSON data -->
                            <input type="hidden" name="question_{{ question.id }}_time_ranges" id="timeRanges_{{ loop.index0 }}" value="">
                        </div>
                    {% else %}
                        <!-- Text-based questions (short answer, paragraph, etc.) -->
                        <div class="text-response-config">
                            {% set question_id = question.id|string %}
                            {% set question_config = form_data.customer_config.questions.get(question_id, {}) if form_data.customer_config else {} %}

                            <h6>Response Guidelines</h6>

                            <div class="mb-3">
                                <label for="question_{{ question.id }}_description" class="form-label">
                                    Description/Requirements{% if question.required %} <span class="text-danger">*</span>{% endif %}
                                </label>
                                <textarea class="form-control" id="question_{{ question.id }}_description"
                                          name="question_{{ question.id }}_description" rows="2"
                                          placeholder="Describe what kind of responses you want for this question"{% if question.required %} required{% endif %}>{{ question_config.get('description', '') }}</textarea>
                                <div class="form-text">
                                    {% if question.required %}
                                    This field is required because the question is marked as required in the Google Form.
                                    {% else %}
                                    Provide guidelines for generating responses to this question.
                                    {% endif %}
                                </div>
                            </div>

                            <h6>Example Responses</h6>
                            <p class="text-muted">Provide up to 3 example responses to guide the response generation.</p>

                            {% for i in range(1, 4) %}
                            {% set example_value = question_config.get('examples', [])[i-1] if question_config.get('examples') and (i-1) < question_config.get('examples')|length else '' %}
                            <div class="mb-3 example-textarea">
                                <label for="question_{{ question.id }}_example_{{ i }}" class="form-label">Example {{ i }}</label>
                                <textarea class="form-control" id="question_{{ question.id }}_example_{{ i }}"
                                          name="question_{{ question.id }}_example_{{ i }}" rows="2"
                                          placeholder="Example response {{ i }}">{{ example_value }}</textarea>
                            </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4 mb-5">
                <a href="{{ url_for('form_details', form_id=form_id) }}" class="btn btn-secondary me-md-2">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
                <button type="button" id="exportTextBtn" class="btn btn-info me-md-2">
                    <i class="fas fa-file-export me-1"></i>Export as Text
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>{{ form.submit.label }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize percentage sliders
        initializePercentageSliders();

        // Form validation
        validateForm();

        // Export to text functionality
        $('#exportTextBtn').on('click', function() {
            exportFormAsText();
        });

        // Function to export form data as text
        function exportFormAsText() {
            // Get form title
            const formTitle = $('h5:first').text().trim();

            // Get total responses
            const totalResponses = $('#total_responses').val();

            // Start building the text
            let fullText = `📋 Form: ${formTitle}\n🔢 Responses: ${totalResponses}\n\n`;

            // Process each question
            $('.question-card').each(function() {
                const questionTitle = $(this).find('.card-header h5').text().trim();
                const questionType = $(this).find('.badge.bg-info').text().trim();

                // Add question info
                fullText += `❓ Question: ${questionTitle.replace(/Required|Optional/g, '').trim()}\n`;

                // Process based on question type
                if (['Multiple Choice', 'Dropdown', 'Checkboxes', 'Linear Scale', 'Rating'].includes(questionType)) {
                    // For option-based questions, get percentages
                    fullText += '✅ Options: ';
                    const options = [];
                    $(this).find('.option-row').each(function() {
                        const optionText = $(this).find('.option-label').text().trim();
                        const percentage = $(this).find('.percentage-display').text().trim();
                        options.push(`${optionText}(${percentage})`);
                    });
                    fullText += options.join(', ') + '\n\n-------------------\n';
                } else {
                    // For text-based questions, get examples
                    const description = $(this).find('textarea[id$="_description"]').val();
                    if (description) {
                        fullText += `📝 Description: ${description}\n`;
                    }

                    // Get examples
                    const examples = [];
                    $(this).find('textarea[id$="_example_1"], textarea[id$="_example_2"], textarea[id$="_example_3"]').each(function() {
                        const example = $(this).val().trim();
                        if (example) {
                            examples.push(example);
                        }
                    });

                    if (examples.length > 0) {
                        fullText += `💬 Examples: ${examples.join(' | ')}\n\n-------------------\n`;
                    }
                }

                // We've already added separators in each condition
            });

            // Split text into chunks of 500 characters or less
            const messages = splitTextIntoMessages(fullText, 500);

            // Create a modal to display the text
            let messagesHtml = '';
            messages.forEach((message, index) => {
                messagesHtml += `
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <span><strong>📩 Message ${index + 1} of ${messages.length}</strong> (${message.length} characters)</span>
                            <button type="button" class="btn btn-sm btn-primary copy-message-btn" data-message-index="${index}">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <div class="card-body">
                            <textarea class="form-control message-textarea" style="font-size: 16px; line-height: 1.5;" rows="6" readonly>${message}</textarea>
                        </div>
                    </div>
                `;
            });

            const modal = `
                <div class="modal fade" id="exportTextModal" tabindex="-1" aria-labelledby="exportTextModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exportTextModalLabel">Export as Text (${messages.length} messages)</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <p><i class="fas fa-info-circle me-2"></i><strong>Shopee Chat Format</strong></p>
                                    <p>💬 Your text has been split into ${messages.length} messages to fit within Shopee's 500 character limit.</p>
                                    <p>📲 For mobile: Copy each message separately and send them in order.</p>
                                </div>
                                ${messagesHtml}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" id="copyAllBtn">
                                    <i class="fas fa-copy me-1"></i>Copy All Messages
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove any existing modal
            $('#exportTextModal').remove();

            // Add the modal to the page
            $('body').append(modal);

            // Show the modal
            const modalElement = new bootstrap.Modal(document.getElementById('exportTextModal'));
            modalElement.show();

            // Add copy functionality for individual messages
            $('.copy-message-btn').on('click', function() {
                const index = $(this).data('message-index');
                const textarea = $(this).closest('.card').find('textarea')[0];
                textarea.select();
                document.execCommand('copy');

                // Show success message
                $(this).html('<i class="fas fa-check"></i> Copied!');
                setTimeout(() => {
                    $(this).html('<i class="fas fa-copy"></i> Copy');
                }, 2000);
            });

            // Add copy all functionality
            $('#copyAllBtn').on('click', function() {
                // Create a temporary textarea with all messages
                const allText = messages.join('\n\n--- NEXT MESSAGE ---\n\n');
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = allText;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Show success message
                $(this).html('<i class="fas fa-check me-1"></i>All Copied!');
                setTimeout(() => {
                    $(this).html('<i class="fas fa-copy me-1"></i>Copy All Messages');
                }, 2000);
            });
        }

        // Function to split text into messages of max length
        function splitTextIntoMessages(text, maxLength) {
            // If text is already short enough, return it as a single message
            if (text.length <= maxLength) {
                return [text];
            }

            const messages = [];
            let remainingText = text;

            while (remainingText.length > 0) {
                if (remainingText.length <= maxLength) {
                    // Add the remaining text as the last message
                    messages.push(remainingText);
                    break;
                }

                // Find a good breaking point (end of a line or paragraph)
                let breakPoint = findBreakPoint(remainingText, maxLength);

                // Extract the current message
                const currentMessage = remainingText.substring(0, breakPoint).trim();
                messages.push(currentMessage);

                // Update the remaining text
                remainingText = remainingText.substring(breakPoint).trim();
            }

            return messages;
        }

        // Function to find a good breaking point in text
        function findBreakPoint(text, maxLength) {
            // If text is shorter than maxLength, return its length
            if (text.length <= maxLength) {
                return text.length;
            }

            // Try to break at the separator line
            const separatorBreak = text.lastIndexOf('-------------------', maxLength);
            if (separatorBreak > maxLength / 2) {
                return separatorBreak + SEPARATOR_LENGTH; // Include the separator
            }

            // Try to break at a double newline (paragraph break)
            const paragraphBreak = text.lastIndexOf('\n\n', maxLength);
            if (paragraphBreak > maxLength / 2) {
                return paragraphBreak + 2; // Include the newlines
            }

            // Try to break at a single newline
            const lineBreak = text.lastIndexOf('\n', maxLength);
            if (lineBreak > maxLength / 2) {
                return lineBreak + 1; // Include the newline
            }

            // Try to break at a space
            const spaceBreak = text.lastIndexOf(' ', maxLength);
            if (spaceBreak > 0) {
                return spaceBreak + 1; // Include the space
            }

            // If no good breaking point, just break at maxLength
            return maxLength;
        }

        // Function to initialize percentage sliders
        function initializePercentageSliders() {
            $('.option-sliders').each(function() {
                const questionId = $(this).data('question-id');
                updateTotalPercentage(questionId);

                // Add event listeners to sliders
                $(this).find('.percentage-slider').on('input', function() {
                    const value = $(this).val();
                    $(this).closest('.option-row').find('.percentage-display').text(value + '%');
                    updateTotalPercentage(questionId);
                });
            });
        }

        // Function to update total percentage
        function updateTotalPercentage(questionId) {
            const sliders = $(`.percentage-slider[data-question-id="${questionId}"]`);
            let total = 0;

            sliders.each(function() {
                total += parseInt($(this).val());
            });

            const totalElement = $(`#total-percentage-${questionId}`);
            totalElement.text(`Total: ${total}%`);

            if (total === 100) {
                totalElement.removeClass('percentage-invalid').addClass('percentage-valid');
            } else {
                totalElement.removeClass('percentage-valid').addClass('percentage-invalid');
            }

            return total === 100;
        }

        // Function to validate form before submission
        function validateForm() {
            $('#customerConfigForm').on('submit', function(e) {
                let isValid = true;

                // Validate total responses
                const totalResponses = $('#total_responses').val();
                if (!totalResponses || totalResponses < 1) {
                    isValid = false;
                    $('#total_responses').addClass('is-invalid');
                } else {
                    $('#total_responses').removeClass('is-invalid');
                }

                // Validate percentage distributions
                $('.option-sliders').each(function() {
                    const questionId = $(this).data('question-id');
                    if (!updateTotalPercentage(questionId)) {
                        isValid = false;
                        $(`#question-${questionId}`).addClass('border-danger');
                    } else {
                        $(`#question-${questionId}`).removeClass('border-danger');
                    }
                });

                // Validate text-based questions (short_answer, paragraph)
                $('.text-response-config').each(function() {
                    const questionCard = $(this).closest('.question-card');
                    const questionTitle = questionCard.find('.card-header h5').text();
                    const isRequired = questionTitle.includes('Required');

                    if (isRequired) {
                        const descriptionField = $(this).find('textarea[id$="_description"]');
                        const descriptionValue = descriptionField.val().trim();

                        // Remove any previous validation styling
                        descriptionField.removeClass('is-invalid');
                        descriptionField.parent().find('.invalid-feedback').remove();

                        if (!descriptionValue) {
                            isValid = false;
                            descriptionField.addClass('is-invalid');

                            // Add error message
                            const feedback = $('<div class="invalid-feedback">This field is required for required questions. Please provide a description or requirements for generating responses.</div>');
                            descriptionField.parent().append(feedback);

                            // Add border to question card
                            questionCard.addClass('border-danger');
                        } else {
                            questionCard.removeClass('border-danger');
                        }
                    }
                });

                // Serialize advanced question configurations before submission
                serializeAdvancedConfigurations();

                if (!isValid) {
                    e.preventDefault();

                    // Scroll to the first invalid element
                    const firstInvalid = $('.is-invalid, .border-danger').first();
                    if (firstInvalid.length) {
                        $('html, body').animate({
                            scrollTop: firstInvalid.offset().top - 100
                        }, 500);
                    }

                    // Show error message
                    showAlert('Please fix the errors before submitting.', 'danger');
                }
            });
        }

        // Function to show alert
        function showAlert(message, type) {
            const alert = `
                <div class="alert alert-${type} alert-dismissible fade show">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Insert alert after the first card
            $('.card:first').after(alert);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        // Advanced question type functions
        function bindAdvancedQuestionEvents() {
            // Multiple Choice Grid combination events
            $(document).on('click', '.add-multi-grid-combination', function() {
                var questionIndex = $(this).data('question');
                addMultiGridCombination(questionIndex);
            });

            $(document).on('click', '.remove-combination', function() {
                $(this).closest('.combination-item').remove();
            });

            // Checkbox Grid combination events
            $(document).on('click', '.add-checkbox-grid-combination', function() {
                var questionIndex = $(this).data('question');
                addCheckboxGridCombination(questionIndex);
            });

            // Checkbox combination events
            $(document).on('click', '.add-checkbox-combination', function() {
                var questionIndex = $(this).data('question');
                addCheckboxCombination(questionIndex);
            });

            // Date range events
            $(document).on('click', '.add-date-range', function() {
                var questionIndex = $(this).data('question');
                addDateRange(questionIndex);
            });

            $(document).on('click', '.remove-range', function() {
                $(this).closest('.range-item').remove();
            });

            // Time range events
            $(document).on('click', '.add-time-range', function() {
                var questionIndex = $(this).data('question');
                addTimeRange(questionIndex);
            });
        }

        // Functions to add new combinations/ranges
        function addMultiGridCombination(questionIndex) {
            var container = $('#multiGridCombinations_' + questionIndex);
            var combinationCount = container.find('.combination-item').length + 1;

            // Get question data from the form
            var questionCard = container.closest('.question-card');
            var rows = [];
            var columns = [];

            questionCard.find('table tbody tr').each(function() {
                var row = $(this).find('td:first strong').text();
                if (row) rows.push(row);
            });

            questionCard.find('table thead th').each(function(index) {
                if (index > 0) { // Skip first column (Row header)
                    var column = $(this).text();
                    if (column) columns.push(column);
                }
            });

            var html = '<div class="combination-item mb-3 p-3 border rounded">' +
                '<h6>Combination ' + combinationCount + '</h6>';

            rows.forEach(function(row, rowIndex) {
                html += '<div class="mb-2">' +
                    '<label class="form-label">' + row + ':</label>' +
                    '<select class="form-select" name="multi_grid_' + questionIndex + '_row_' + rowIndex + '">' +
                        '<option value="">-- Select Option --</option>';

                columns.forEach(function(column) {
                    html += '<option value="' + column + '">' + column + '</option>';
                });

                html += '</select></div>';
            });

            html += '<div class="mb-2">' +
                '<label class="form-label">Weight:</label>' +
                '<input type="number" class="form-control" name="multi_grid_' + questionIndex + '_weight" value="1" min="1">' +
            '</div>' +
            '<button type="button" class="btn btn-sm btn-danger remove-combination">Remove</button>' +
            '</div>';

            container.append(html);
        }

        function addCheckboxGridCombination(questionIndex) {
            var container = $('#checkboxGridCombinations_' + questionIndex);
            var combinationCount = container.find('.combination-item').length + 1;

            // Get question data from the form
            var questionCard = container.closest('.question-card');
            var rows = [];
            var columns = [];

            questionCard.find('table tbody tr').each(function() {
                var row = $(this).find('td:first strong').text();
                if (row) rows.push(row);
            });

            questionCard.find('table thead th').each(function(index) {
                if (index > 0) { // Skip first column (Row header)
                    var column = $(this).text();
                    if (column) columns.push(column);
                }
            });

            var html = '<div class="combination-item mb-3 p-3 border rounded">' +
                '<h6>Combination ' + combinationCount + '</h6>';

            rows.forEach(function(row, rowIndex) {
                html += '<div class="mb-2">' +
                    '<label class="form-label">' + row + ':</label>' +
                    '<div class="checkbox-group">';

                columns.forEach(function(column, colIndex) {
                    var checkboxId = 'cb_' + questionIndex + '_' + combinationCount + '_' + rowIndex + '_' + colIndex;
                    html += '<div class="form-check form-check-inline">' +
                        '<input class="form-check-input" type="checkbox" ' +
                            'name="checkbox_grid_' + questionIndex + '_row_' + rowIndex + '[]" ' +
                            'value="' + column + '" id="' + checkboxId + '">' +
                        '<label class="form-check-label" for="' + checkboxId + '">' +
                            column +
                        '</label>' +
                    '</div>';
                });

                html += '</div></div>';
            });

            html += '<div class="mb-2">' +
                '<label class="form-label">Weight:</label>' +
                '<input type="number" class="form-control" name="checkbox_grid_' + questionIndex + '_weight" value="1" min="1">' +
            '</div>' +
            '<button type="button" class="btn btn-sm btn-danger remove-combination">Remove</button>' +
            '</div>';

            container.append(html);
        }

        function addCheckboxCombination(questionIndex) {
            var container = $('#checkboxCombinations_' + questionIndex);
            var combinationCount = container.find('.combination-item').length + 1;

            // Get question options from the form
            var questionCard = container.closest('.question-card');
            var options = [];

            questionCard.find('.badge').each(function() {
                var option = $(this).text();
                if (option) options.push(option);
            });

            var html = '<div class="combination-item mb-3 p-3 border rounded">' +
                '<h6>Combination ' + combinationCount + '</h6>' +
                '<div class="mb-2">' +
                    '<label class="form-label">Select Options:</label>' +
                    '<div class="checkbox-group">';

            options.forEach(function(option, optIndex) {
                var checkboxId = 'cb_opt_' + questionIndex + '_' + combinationCount + '_' + optIndex;
                html += '<div class="form-check">' +
                    '<input class="form-check-input" type="checkbox" ' +
                        'name="checkbox_' + questionIndex + '_options[]" ' +
                        'value="' + option + '" id="' + checkboxId + '">' +
                    '<label class="form-check-label" for="' + checkboxId + '">' +
                        option +
                    '</label>' +
                '</div>';
            });

            html += '</div></div>' +
                '<div class="mb-2">' +
                    '<label class="form-label">Weight:</label>' +
                    '<input type="number" class="form-control" name="checkbox_' + questionIndex + '_weight" value="1" min="1">' +
                '</div>' +
                '<button type="button" class="btn btn-sm btn-danger remove-combination">Remove</button>' +
                '</div>';

            container.append(html);
        }

        function addDateRange(questionIndex) {
            var container = $('#dateRanges_' + questionIndex);
            var rangeCount = container.find('.range-item').length + 1;

            var html = '<div class="range-item mb-3 p-3 border rounded">' +
                '<h6>Date Range ' + rangeCount + '</h6>' +
                '<div class="row">' +
                    '<div class="col-md-5">' +
                        '<label class="form-label">Start Date:</label>' +
                        '<input type="date" class="form-control" name="date_' + questionIndex + '_start">' +
                    '</div>' +
                    '<div class="col-md-5">' +
                        '<label class="form-label">End Date:</label>' +
                        '<input type="date" class="form-control" name="date_' + questionIndex + '_end">' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<label class="form-label">Weight:</label>' +
                        '<input type="number" class="form-control" name="date_' + questionIndex + '_weight" value="1" min="1" required>' +
                    '</div>' +
                '</div>' +
                '<div class="mt-3">' +
                    '<button type="button" class="btn btn-sm btn-danger remove-range">' +
                        '<i class="fas fa-trash me-1"></i>Remove Range' +
                    '</button>' +
                '</div>' +
            '</div>';

            container.append(html);
        }

        function addTimeRange(questionIndex) {
            var container = $('#timeRanges_' + questionIndex);
            var rangeCount = container.find('.range-item').length + 1;

            var html = '<div class="range-item mb-3 p-3 border rounded">' +
                '<h6>Time Range ' + rangeCount + '</h6>' +
                '<div class="row">' +
                    '<div class="col-md-5">' +
                        '<label class="form-label">Start Time:</label>' +
                        '<input type="time" class="form-control" name="time_' + questionIndex + '_start">' +
                    '</div>' +
                    '<div class="col-md-5">' +
                        '<label class="form-label">End Time:</label>' +
                        '<input type="time" class="form-control" name="time_' + questionIndex + '_end">' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<label class="form-label">Weight:</label>' +
                        '<input type="number" class="form-control" name="time_' + questionIndex + '_weight" value="1" min="1" required>' +
                    '</div>' +
                '</div>' +
                '<div class="mt-3">' +
                    '<button type="button" class="btn btn-sm btn-danger remove-range">' +
                        '<i class="fas fa-trash me-1"></i>Remove Range' +
                    '</button>' +
                '</div>' +
            '</div>';

            container.append(html);
        }

        // Function to serialize advanced configurations to JSON
        function serializeAdvancedConfigurations() {
            // Serialize multi-grid combinations
            $('.multi-grid-combinations').each(function() {
                var questionIndex = $(this).attr('id').split('_')[1];
                var combinations = [];

                $(this).find('.combination-item').each(function() {
                    var combination = {};
                    var weight = $(this).find('input[name$="_weight"]').val();

                    $(this).find('select').each(function() {
                        var name = $(this).attr('name');
                        var value = $(this).val();
                        if (value) {
                            combination[name] = value;
                        }
                    });

                    if (Object.keys(combination).length > 0) {
                        combination.weight = parseInt(weight) || 1;
                        combinations.push(combination);
                    }
                });

                $('#multiGridData_' + questionIndex).val(JSON.stringify(combinations));
            });

            // Serialize checkbox-grid combinations
            $('.checkbox-grid-combinations').each(function() {
                var questionIndex = $(this).attr('id').split('_')[1];
                var combinations = [];

                $(this).find('.combination-item').each(function() {
                    var combination = {};
                    var weight = $(this).find('input[name$="_weight"]').val();

                    $(this).find('input[type="checkbox"]:checked').each(function() {
                        var name = $(this).attr('name');
                        var value = $(this).val();
                        if (!combination[name]) combination[name] = [];
                        combination[name].push(value);
                    });

                    if (Object.keys(combination).length > 0) {
                        combination.weight = parseInt(weight) || 1;
                        combinations.push(combination);
                    }
                });

                $('#checkboxGridData_' + questionIndex).val(JSON.stringify(combinations));
            });

            // Serialize checkbox combinations
            $('.checkbox-combinations').each(function() {
                var questionIndex = $(this).attr('id').split('_')[1];
                var combinations = [];

                $(this).find('.combination-item').each(function() {
                    var combination = {};
                    var weight = $(this).find('input[name$="_weight"]').val();
                    var options = [];

                    $(this).find('input[type="checkbox"]:checked').each(function() {
                        options.push($(this).val());
                    });

                    if (options.length > 0) {
                        combination.options = options;
                        combination.weight = parseInt(weight) || 1;
                        combinations.push(combination);
                    }
                });

                $('#checkboxData_' + questionIndex).val(JSON.stringify(combinations));
            });

            // Serialize date ranges
            $('.date-ranges').each(function() {
                var questionIndex = $(this).attr('id').split('_')[1];
                var ranges = [];

                $(this).find('.range-item').each(function() {
                    var startDate = $(this).find('input[name$="_start"]').val();
                    var endDate = $(this).find('input[name$="_end"]').val();
                    var weight = $(this).find('input[name$="_weight"]').val();

                    if (startDate && endDate) {
                        ranges.push({
                            start: startDate,
                            end: endDate,
                            weight: parseInt(weight) || 1
                        });
                    }
                });

                $('#dateRanges_' + questionIndex).val(JSON.stringify(ranges));
            });

            // Serialize time ranges
            $('.time-ranges').each(function() {
                var questionIndex = $(this).attr('id').split('_')[1];
                var ranges = [];

                $(this).find('.range-item').each(function() {
                    var startTime = $(this).find('input[name$="_start"]').val();
                    var endTime = $(this).find('input[name$="_end"]').val();
                    var weight = $(this).find('input[name$="_weight"]').val();

                    if (startTime && endTime) {
                        ranges.push({
                            start: startTime,
                            end: endTime,
                            weight: parseInt(weight) || 1
                        });
                    }
                });

                $('#timeRanges_' + questionIndex).val(JSON.stringify(ranges));
            });
        }

        // Initialize advanced question events
        bindAdvancedQuestionEvents();
    });
</script>
{% endblock %}
