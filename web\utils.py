"""
Utils Module for Google Form AutoFill Web Interface

This module provides utility functions for the web interface.
"""

import os
from src.core.response_storage import <PERSON><PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>


def calculate_submittable_forms_count(form_data):
    """
    Calculate how many complete forms can be submitted based on required questions
    
    Args:
        form_data: The form data dictionary
        
    Returns:
        Number of submittable complete forms (limited by required questions)
    """
    if not form_data or not form_data.get('questions'):
        return 0
    
    required_response_counts = []
    
    for question in form_data.get('questions', []):
        if question.get('required', False):
            q_id = str(question.get('id'))
            responses = form_data.get('responses', {}).get(q_id, [])
            required_response_counts.append(len(responses))
    
    # If no required questions, return 0 (can't submit incomplete forms)
    if not required_response_counts:
        return 0
    
    # Return minimum count from required questions (bottleneck)
    return min(required_response_counts)


def calculate_total_review_responses(form_data):
    """
    Calculate total number of review responses across all questions
    
    Args:
        form_data: The form data dictionary
        
    Returns:
        Total number of review responses
    """
    if not form_data:
        return 0
    
    return sum(len(responses) for responses in form_data.get('responses', {}).values())


def get_response_breakdown(form_data):
    """
    Get detailed breakdown of responses by question type
    
    Args:
        form_data: The form data dictionary
        
    Returns:
        Dictionary with response breakdown information
    """
    if not form_data:
        return {
            'required_questions': 0,
            'optional_questions': 0,
            'required_responses': [],
            'optional_responses': [],
            'submittable_count': 0,
            'total_review_responses': 0
        }
    
    required_responses = []
    optional_responses = []
    
    for question in form_data.get('questions', []):
        q_id = str(question.get('id'))
        q_title = question.get('title', 'Untitled')
        is_required = question.get('required', False)
        responses = form_data.get('responses', {}).get(q_id, [])
        response_count = len(responses)
        
        question_info = {
            'title': q_title,
            'response_count': response_count
        }
        
        if is_required:
            required_responses.append(question_info)
        else:
            optional_responses.append(question_info)
    
    submittable_count = calculate_submittable_forms_count(form_data)
    total_review_responses = calculate_total_review_responses(form_data)
    
    return {
        'required_questions': len(required_responses),
        'optional_questions': len(optional_responses),
        'required_responses': required_responses,
        'optional_responses': optional_responses,
        'submittable_count': submittable_count,
        'total_review_responses': total_review_responses
    }


def get_saved_forms():
    """
    Get list of saved forms with accurate metadata including response breakdown
    
    Returns:
        List of dictionaries with form metadata
    """
    manager = FormResponseManager()
    forms = []
    
    for form_id in manager.storage.list_saved_forms():
        form_data = manager.load_form_data(form_id)
        if form_data:
            # Get accurate response calculations
            submittable_count = calculate_submittable_forms_count(form_data)
            total_review_responses = calculate_total_review_responses(form_data)
            breakdown = get_response_breakdown(form_data)
            
            forms.append({
                'id': form_id,
                'title': form_data.get('form_title', 'Untitled Form'),
                'question_count': len(form_data.get('questions', [])),
                # Legacy field for backward compatibility
                'response_count': submittable_count,  
                # New detailed fields
                'submittable_count': submittable_count,
                'total_review_responses': total_review_responses,
                'required_questions': breakdown['required_questions'],
                'optional_questions': breakdown['optional_questions'],
                'breakdown': breakdown
            })
    
    return forms


def allowed_file(filename):
    """
    Check if a file has an allowed extension
    
    Args:
        filename: The filename to check
        
    Returns:
        True if the file has an allowed extension, False otherwise
    """
    ALLOWED_EXTENSIONS = {'json'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def format_timestamp(timestamp):
    """
    Format a timestamp as a human-readable string
    
    Args:
        timestamp: The timestamp to format
        
    Returns:
        Formatted timestamp string
    """
    from datetime import datetime
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')


def get_question_by_id(form_data, question_id):
    """
    Get a question by its ID
    
    Args:
        form_data: The form data
        question_id: The ID of the question
        
    Returns:
        The question object, or None if not found
    """
    for question in form_data.get('questions', []):
        if question['id'] == question_id:
            return question
    return None


def get_question_index(form_data, question_id):
    """
    Get the index of a question by its ID
    
    Args:
        form_data: The form data
        question_id: The ID of the question
        
    Returns:
        The 1-based index of the question, or None if not found
    """
    for i, question in enumerate(form_data.get('questions', []), 1):
        if question['id'] == question_id:
            return i
    return None
